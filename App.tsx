import React from "react";
import { LogBox } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { SafeAreaProvider } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import { Provider } from "react-redux";
import NetworkLogger from "./src/Components/NetworkLogger";
import ThemedStatusBar from "./src/Components/ThemedStatusBar";
import { LanguageProvider } from "./src/Localization/LanguageContext";
import Routes from "./src/Navigation/Routes";
import { store } from "./src/Redux/store";
import { ThemeProvider } from "./src/Utilities/ThemeContext";

// Initialize i18n
import "./src/Localization/i18n";

LogBox.ignoreAllLogs();

export default function App() {
  return (
    <>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <Provider store={store}>
          <LanguageProvider>
            <ThemeProvider>
              <ThemedStatusBar />
              <SafeAreaProvider>
                <Routes />
                {__DEV__ && <NetworkLogger />}
              </SafeAreaProvider>
            </ThemeProvider>
          </LanguageProvider>
        </Provider>
      </GestureHandlerRootView>
      <Toast />
    </>
  );
}

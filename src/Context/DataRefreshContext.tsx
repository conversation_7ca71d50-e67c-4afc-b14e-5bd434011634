import React, { createContext, useContext, useCallback, ReactNode } from 'react';
import { useDataManager } from '../Hooks/useDataManager';

// Define the context type
interface DataRefreshContextType {
  // Refresh functions for individual data types
  refreshClients: () => Promise<void>;
  refreshMembers: () => Promise<void>;
  refreshCategories: () => Promise<void>;
  refreshServices: () => Promise<void>;
  refreshPackages: () => Promise<void>;
  
  // Refresh all data
  refreshAllData: () => Promise<void>;
  
  // Trigger refresh after data mutations
  onClientAdded: () => Promise<void>;
  onClientUpdated: () => Promise<void>;
  onClientDeleted: () => Promise<void>;
  
  onMemberAdded: () => Promise<void>;
  onMemberUpdated: () => Promise<void>;
  onMemberDeleted: () => Promise<void>;
  
  onCategoryAdded: () => Promise<void>;
  onCategoryUpdated: () => Promise<void>;
  onCategoryDeleted: () => Promise<void>;
  
  onServiceAdded: () => Promise<void>;
  onServiceUpdated: () => Promise<void>;
  onServiceDeleted: () => Promise<void>;
  
  onPackageAdded: () => Promise<void>;
  onPackageUpdated: () => Promise<void>;
  onPackageDeleted: () => Promise<void>;
}

// Create the context
const DataRefreshContext = createContext<DataRefreshContextType | undefined>(undefined);

// Provider component
interface DataRefreshProviderProps {
  children: ReactNode;
}

export const DataRefreshProvider: React.FC<DataRefreshProviderProps> = ({ children }) => {
  const { refreshData, fetchAllData } = useDataManager();

  // Individual refresh functions
  const refreshClients = useCallback(async () => {
    await refreshData('clients');
  }, [refreshData]);

  const refreshMembers = useCallback(async () => {
    await refreshData('members');
  }, [refreshData]);

  const refreshCategories = useCallback(async () => {
    await refreshData('categories');
  }, [refreshData]);

  const refreshServices = useCallback(async () => {
    await refreshData('services');
  }, [refreshData]);

  const refreshPackages = useCallback(async () => {
    await refreshData('packages');
  }, [refreshData]);

  // Refresh all data
  const refreshAllData = useCallback(async () => {
    await fetchAllData(true);
  }, [fetchAllData]);

  // Client mutation handlers
  const onClientAdded = useCallback(async () => {
    await refreshClients();
  }, [refreshClients]);

  const onClientUpdated = useCallback(async () => {
    await refreshClients();
  }, [refreshClients]);

  const onClientDeleted = useCallback(async () => {
    await refreshClients();
  }, [refreshClients]);

  // Member mutation handlers
  const onMemberAdded = useCallback(async () => {
    await refreshMembers();
  }, [refreshMembers]);

  const onMemberUpdated = useCallback(async () => {
    await refreshMembers();
  }, [refreshMembers]);

  const onMemberDeleted = useCallback(async () => {
    await refreshMembers();
  }, [refreshMembers]);

  // Category mutation handlers
  const onCategoryAdded = useCallback(async () => {
    await refreshCategories();
    // Categories affect services, so refresh services too
    await refreshServices();
  }, [refreshCategories, refreshServices]);

  const onCategoryUpdated = useCallback(async () => {
    await refreshCategories();
    await refreshServices();
  }, [refreshCategories, refreshServices]);

  const onCategoryDeleted = useCallback(async () => {
    await refreshCategories();
    await refreshServices();
    // Packages might also be affected if they belong to deleted categories
    await refreshPackages();
  }, [refreshCategories, refreshServices, refreshPackages]);

  // Service mutation handlers
  const onServiceAdded = useCallback(async () => {
    await refreshServices();
  }, [refreshServices]);

  const onServiceUpdated = useCallback(async () => {
    await refreshServices();
    // Packages might be affected if they contain the updated service
    await refreshPackages();
  }, [refreshServices, refreshPackages]);

  const onServiceDeleted = useCallback(async () => {
    await refreshServices();
    await refreshPackages();
  }, [refreshServices, refreshPackages]);

  // Package mutation handlers
  const onPackageAdded = useCallback(async () => {
    await refreshPackages();
  }, [refreshPackages]);

  const onPackageUpdated = useCallback(async () => {
    await refreshPackages();
  }, [refreshPackages]);

  const onPackageDeleted = useCallback(async () => {
    await refreshPackages();
  }, [refreshPackages]);

  const contextValue: DataRefreshContextType = {
    // Individual refresh functions
    refreshClients,
    refreshMembers,
    refreshCategories,
    refreshServices,
    refreshPackages,
    refreshAllData,
    
    // Mutation handlers
    onClientAdded,
    onClientUpdated,
    onClientDeleted,
    
    onMemberAdded,
    onMemberUpdated,
    onMemberDeleted,
    
    onCategoryAdded,
    onCategoryUpdated,
    onCategoryDeleted,
    
    onServiceAdded,
    onServiceUpdated,
    onServiceDeleted,
    
    onPackageAdded,
    onPackageUpdated,
    onPackageDeleted,
  };

  return (
    <DataRefreshContext.Provider value={contextValue}>
      {children}
    </DataRefreshContext.Provider>
  );
};

// Hook to use the context
export const useDataRefresh = (): DataRefreshContextType => {
  const context = useContext(DataRefreshContext);
  if (context === undefined) {
    throw new Error('useDataRefresh must be used within a DataRefreshProvider');
  }
  return context;
};

// Higher-order component for automatic data refresh
export const withDataRefresh = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  return (props: P) => (
    <DataRefreshProvider>
      <Component {...props} />
    </DataRefreshProvider>
  );
};

// Hook for automatic refresh on screen focus
export const useRefreshOnFocus = (
  refreshFunction: () => Promise<void>,
  enabled: boolean = true
) => {
  const { useIsFocused } = require('@react-navigation/native');
  const isFocused = useIsFocused();

  React.useEffect(() => {
    if (enabled && isFocused) {
      refreshFunction();
    }
  }, [isFocused, enabled, refreshFunction]);
};

// Hook for pull-to-refresh functionality
export const usePullToRefresh = (
  refreshFunction: () => Promise<void>
) => {
  const [refreshing, setRefreshing] = React.useState(false);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    try {
      await refreshFunction();
    } finally {
      setRefreshing(false);
    }
  }, [refreshFunction]);

  return {
    refreshing,
    onRefresh,
  };
};

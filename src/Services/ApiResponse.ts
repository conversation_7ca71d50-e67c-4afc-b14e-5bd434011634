export interface LoginApiResponse {
  fullName: string;
  email: string;
  phoneNumber: string;
  countryCode: string;
  isVerified: boolean;
  verificationMethod: string;
  isActive: boolean;
  isDeleted: boolean;
  token: any;
  fcmToken: any;
  deviceId: any;
  authType: string;
  profilePic: string;
  languages: string[];
  role: string;
  businessRole: string;
  _id: string;
  identifierId: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  verificationToken: string;
}

export interface SignupApiResponse {
  fullName: string;
  email: string;
  phoneNumber: string;
  countryCode: string;
  isVerified: boolean;
  verificationMethod: string;
  isActive: boolean;
  isDeleted: boolean;
  token: any;
  fcmToken: any;
  deviceId: any;
  authType: string;
  profilePic: string;
  languages: string[];
  role: string;
  businessRole: string;
  _id: string;
  identifierId: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  verificationToken: string;
}

export interface GetProfileApiResponse {
  profile: Profile;
}

export interface Profile {
  userId: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  countryCode: string;
  countryCallingCode: string;
  profilePicture: string;
  isVerified: boolean;
  verificationMethod: string;
  isActive: boolean;
  isDeleted: boolean;
  authType: string;
  role: string;
  businessRole: string;
  identifierId: string;
  business: Business;
}

export interface Business {
  businessId: string;
  businessName: string;
}

export interface GetAllCategoriesApiResponse {
  categories: Category[];
}

export interface Category {
  _id: string;
  name: string;
  description: string;
  icon: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface GetBusinessApiResponse {
  businessProfile: BusinessProfile;
}

export interface BusinessProfile {
  address: Address;
  businessHours: BusinessHours;
  _id: string;
  businessName: string;
  businessDescription: string;
  businessProfilePic: string;
  PhoneNumber: string;
  countryCode: string;
  countryCallingCode: string;
  email: string;
  websiteLink: string;
  facebookLink: string;
  instagramLink: string;
  messengerLink: string;
  country: string;
  selectedCategories: any[];
  status: string;
  ownerId: string;
  isDeleted: boolean;
  businessId: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface Address {
  street: string;
  city: string;
  region: string;
  country: string;
}

export interface BusinessHours {
  monday: Monday;
  tuesday: Tuesday;
  wednesday: Wednesday;
  thursday: Thursday;
  friday: Friday;
  saturday: Saturday;
  sunday: Sunday;
}

export interface Monday {
  isOpen: boolean;
  timeSlots: TimeSlot[];
}

export interface TimeSlot {
  open: string;
  close: string;
}

export interface Tuesday {
  isOpen: boolean;
  timeSlots: TimeSlot2[];
}

export interface TimeSlot2 {
  open: string;
  close: string;
}

export interface Wednesday {
  isOpen: boolean;
  timeSlots: TimeSlot3[];
}

export interface TimeSlot3 {
  open: string;
  close: string;
}

export interface Thursday {
  isOpen: boolean;
  timeSlots: TimeSlot4[];
}

export interface TimeSlot4 {
  open: string;
  close: string;
}

export interface Friday {
  isOpen: boolean;
  timeSlots: TimeSlot5[];
}

export interface TimeSlot5 {
  open: string;
  close: string;
}

export interface Saturday {
  isOpen: boolean;
  timeSlots: TimeSlot6[];
}

export interface TimeSlot6 {
  open: string;
  close: string;
}

export interface Sunday {
  isOpen: boolean;
  timeSlots: TimeSlot7[];
}

export interface TimeSlot7 {
  open: string;
  close: string;
}

export interface GetAllUserCategoriesApiResponse {
  categories: Category[];
  pagination: Pagination;
}

export interface Category {
  _id: string;
  name: string;
  description: string;
  icon: string;
  businessId: string;
  isActive: boolean;
  isDeleted: boolean;
  isGlobal: boolean;
}

export interface Pagination {
  totalItems: number;
  totalPages: number;
  currentPage: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface AddNewCategoryAPiResponse {
  category: Category;
}

export interface Category {
  name: string;
  description: string;
  businessId: string;
  isActive: boolean;
  isDeleted: boolean;
  _id: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface GetAllClientsApiResponse {
  clients: Client[];
  pagination: Pagination;
}

export interface Client {
  address: Address;
  _id: string;
  name: string;
  email: string;
  phoneNumber: string;
  countryCode: string;
  countryCallingCode: string;
  profilePicture: string;
  birthday: string;
  gender: string;
  tags: any[];
  businessId: string;
  preferredServices: any[];
  preferredTeamMembers: any[];
  lastVisit: any;
  isActive: boolean;
  isDeleted: boolean;
  clientId: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface Address {
  street: string;
  city: string;
  region: string;
  country: string;
}

export interface Pagination {
  totalClients: number;
  totalPages: number;
  currentPage: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface AddNewClientApiResponse {
  client: Client;
}

export interface Client {
  name: string;
  email: string;
  phoneNumber: string;
  countryCode: string;
  countryCallingCode: string;
  profilePicture: string;
  birthday: string;
  gender: string;
  address: Address;
  tags: any[];
  businessId: string;
  preferredServices: any[];
  preferredTeamMembers: any[];
  lastVisit: any;
  isActive: boolean;
  isDeleted: boolean;
  _id: string;
  clientId: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface Address {
  street: string;
  city: string;
  region: string;
  country: string;
}

export interface APIErrorResponse {
  success: boolean;
  message: string;
  status: number;
}

export interface GetAllTeamMembersApiResponse {
  teamMembers: TeamMember[];
  pagination: Pagination;
}

export interface TeamMember {
  permissions: Permissions;
  countryCallingCode: string;
  _id: string;
  name: string;
  email: string;
  phoneNumber: string;
  countryCode: string;
  profilePicture: string;
  birthday: string;
  gender: string;
  businessId: string;
  role: string;
  specialization: string;
  employmentStatus: string;
  userId: string;
  isActive: boolean;
  isDeleted: boolean;
  memberId: string;
  services: any[];
  joinDate: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface Permissions {
  canManageAppointments: boolean;
  canManageClients: boolean;
  canManageServices: boolean;
  canManageTeam: boolean;
  canManageSettings: boolean;
  canAccessReports: boolean;
}

export interface Pagination {
  totalTeamMembers: number;
  totalPages: number;
  currentPage: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface AddNewTeamMemberApiResponse {
  teamMember: TeamMember;
}

export interface TeamMember {
  name: string;
  email: string;
  phoneNumber: string;
  countryCode: string;
  countryCallingCode: string;
  profilePicture: string;
  birthday: string;
  gender: string;
  businessId: string;
  role: string;
  specialization: string;
  employmentStatus: string;
  userId: string;
  isActive: boolean;
  isDeleted: boolean;
  permissions: Permissions;
  _id: string;
  memberId: string;
  services: any[];
  joinDate: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface Permissions {
  canManageAppointments: boolean;
  canManageClients: boolean;
  canManageServices: boolean;
  canManageTeam: boolean;
  canManageSettings: boolean;
  canAccessReports: boolean;
}

export interface GetAllServicesApiResponse {
  categoriesWithServices: CategoriesWithService[];
}

export interface CategoriesWithService {
  _id: string;
  name: string;
  description: string;
  isGlobal: boolean;
  services: Service[];
  icon?: string;
}

export interface Service {
  isGlobalCategory: boolean;
  _id: string;
  name: string;
  categoryId: string;
  categoryName: string;
  description: string;
  duration: number;
  priceType: string;
  price: number;
  maxPrice: any;
  currency: string;
  businessId: string;
  teamMembers: TeamMember[];
  icon: string;
  tags: string[];
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface TeamMember {
  _id: string;
  name: string;
  email: string;
  phoneNumber: string;
  countryCode: string;
  countryCallingCode: string;
  profilePicture: string;
  gender: string;
  specialization: string;
  role: string;
}

export interface GetAllPackagesApiResponse {
  packages: Package[];
  pagination: Pagination;
}

export interface Package {
  _id: string;
  name: string;
  categoryId: string;
  categoryName: string;
  description: string;
  services: Service[];
  duration: number;
  priceType: string;
  price: number;
  maxPrice: any;
  discountPercentage: number;
  discountAmount: number;
  finalPrice: number;
  currency: string;
  businessId: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface Service {
  serviceId: string;
  name: string;
  duration: number;
  price: number;
  _id: string;
}

export interface Pagination {
  totalItems: number;
  totalPages: number;
  currentPage: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface AddNewServiceApiResponse {
  service: Service;
}

export interface Service {
  name: string;
  categoryId: string;
  categoryName: string;
  description: string;
  duration: number;
  priceType: string;
  price: number;
  maxPrice: any;
  currency: string;
  businessId: string;
  teamMembers: TeamMember[];
  icon: string;
  isActive: boolean;
  isDeleted: boolean;
  isGlobalCategory: boolean;
  _id: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface TeamMember {
  _id: string;
  name: string;
  email: string;
  phoneNumber: string;
  countryCode: string;
  countryCallingCode: string;
  profilePicture: string;
  gender: string;
  specialization: string;
  role: string;
}
import React, { <PERSON> } from "react";
import { ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import UnifiedHeader from "../../Components/UnifiedHeader";
import useTranslation from "../../Localization/useTranslation";
import { useThemedStyles } from "./themedStyles";
import { TermsOfServiceProps } from "../../Navigation/Typings";

const TermsOfService: FC<TermsOfServiceProps> = ({ navigation }) => {
  const styles = useThemedStyles();
  const { termsOfService, common } = useTranslation();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <UnifiedHeader
          title={termsOfService("termsOfService")}
          onBackPress={() => navigation.goBack()}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default TermsOfService;

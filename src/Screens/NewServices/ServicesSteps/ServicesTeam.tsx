import React, { useEffect, useState } from "react";
import { FlatList, Image, Text, TouchableOpacity, View } from "react-native";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { useAppSelector } from "../../../Redux/store";
import { moderateScale } from "../../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedServicesTeamStyles } from "./themedServicesTeamStyles";
import useTranslation from "../../../Localization/useTranslation";

const ServicesTeam = ({ teamMembers, setTeamMembers }: any) => {
  const { colors } = useTheme();
  const styles = useThemedServicesTeamStyles();

  const { services: servicesTranslation } = useTranslation();

  // Get all available team members from Redux store
  const { members } = useAppSelector((state) => state.members);

  // Initialize the member list with checkboxes
  const [memberListWithCheckBox, setMemberListWithCheckBox] = useState<
    Array<any & { checked: boolean }>
  >([]);

  // Initialize selectAll based on whether all members are checked
  const [selectAll, setSelectAll] = useState(false);

  // Update the member list when members from Redux or teamMembers prop changes
  useEffect(() => {
    if (members && members.length > 0) {
      // Create a list of all available team members with checked status
      const updatedMembers = members.map((member: any) => {
        // Check if this member is already assigned to the service (when editing)
        const isAssigned = teamMembers.some(
          (assignedMember: any) => assignedMember._id === member.id
        );
        return {
          ...member,
          checked: isAssigned,
        };
      });

      setMemberListWithCheckBox(updatedMembers);

      // Update selectAll based on whether all members are checked
      const allChecked =
        updatedMembers.length > 0 &&
        updatedMembers.every((member: any) => member.checked);
      setSelectAll(allChecked);
    }
  }, []);

  const toggleCheckbox = (id: string) => {
    setMemberListWithCheckBox((prevMembers) => {
      const updatedMembers = prevMembers.map((member) => {
        const memberId = member._id || member.id;
        const shouldToggle = memberId === id;

        return shouldToggle ? { ...member, checked: !member.checked } : member;
      });

      // Update selectAll based on whether all members are checked
      const allChecked = updatedMembers.every((member) => member.checked);
      setSelectAll(allChecked);

      // Update parent component with selected members - format for API
      const selectedMembers = updatedMembers
        .filter((member) => member.checked)
        .map(({ checked, ...member }) => ({
          _id: member._id || member.id,
          name: member.name,
          email: member.email,
          profilePicture: member.profilePicture || member.image,
        }));
      setTeamMembers(selectedMembers);

      return updatedMembers;
    });
  };

  const toggleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);

    setMemberListWithCheckBox((prevMembers) => {
      const updatedMembers = prevMembers.map((member) => ({
        ...member,
        checked: newSelectAll,
      }));

      // Update parent component with selected members - format for API
      const selectedMembers = newSelectAll
        ? updatedMembers.map(({ checked, ...member }) => ({
            _id: member._id || member.id,
            name: member.name,
            email: member.email,
            profilePicture: member.profilePicture || member.image,
          }))
        : [];
      setTeamMembers(selectedMembers);

      return updatedMembers;
    });
  };

  const renderItem = ({ item }: { item: any & { checked: boolean } }) => {
    const itemId = item._id || item.id;

    return (
      <TouchableOpacity
        style={styles.memberItem}
        onPress={() => toggleCheckbox(itemId)}
        activeOpacity={0.8}
      >
        <View
          style={[
            styles.checkbox,
            item.checked && { backgroundColor: colors.primaryBase },
          ]}
        >
          {item.checked && (
            <CustomIcon Icon={ICONS.CheckRightIcon} height={12} width={12} />
          )}
        </View>
        <Image
          source={{ uri: item.profilePicture || item.image }}
          style={{ width: 40, height: 40, marginRight: 10, borderRadius: 100 }}
        />
        <View style={styles.memberInfo}>
          <Text style={styles.memberName}>{item.name}</Text>
          <Text style={styles.memberEmail}>{item.email}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.header}>{servicesTranslation("teammebers")}</Text>
      <Text style={styles.subHeader}>
        {servicesTranslation("selectTeamMembers")}
      </Text>

      {/* Select All Option */}
      <TouchableOpacity
        style={{
          flexDirection: "row",
          alignItems: "center",
          gap: moderateScale(5),
          marginVertical: moderateScale(15),
          paddingLeft: 12,
        }}
        onPress={toggleSelectAll}
      >
        <View
          style={[
            styles.checkbox,
            selectAll && { backgroundColor: colors.primaryBase },
          ]}
        >
          {selectAll && (
            <CustomIcon Icon={ICONS.CheckRightIcon} height={12} width={12} />
          )}
        </View>
        <Text
          style={{ fontSize: 12, fontWeight: "400", color: colors.greyText }}
        >
          {servicesTranslation("allTeamMembers")}
        </Text>
      </TouchableOpacity>

      {/* Team Members List */}
      {memberListWithCheckBox.length > 0 ? (
        <FlatList
          data={memberListWithCheckBox}
          keyExtractor={(item) => item._id || item.id}
          renderItem={renderItem}
        />
      ) : (
        <Text style={styles.emptyText}>No team members available</Text>
      )}
    </View>
  );
};

export default ServicesTeam;

import {
  FlatList,
  Image,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useState, useEffect } from 'react';
import CustomIcon from '../../../Components/CustomIcon';
import ICONS from '../../../Assets/Icon';
import { useTheme } from '../../../Utilities/ThemeContext';
import { useThemedCommonStyles } from '../../../Utilities/Styles/themedCommonStyles';
import { moderateScale, verticalScale } from '../../../Utilities/Styles/responsiveSize';
import { useAppSelector } from '../../../Redux/store';
import { useThemedServicesTeamStyles } from './themedServicesTeamStyles';

const ServicesTeam = ({ teamMembers, setTeamMembers }: any) => {
  const { members } = useAppSelector((state) => state.members);
  const { colors } = useTheme();
  const styles = useThemedServicesTeamStyles();
  const themedCommonStyles = useThemedCommonStyles();

  // Initialize the member list with checkboxes
  // If a member is in the teamMembers array, mark it as checked
  const [memberListWithCheckBox, setMemberListWithCheckBox] = useState(() => {
    return members.map((member: any) => ({
      ...member,
      checked: teamMembers
        ? teamMembers.some((tm: any) => tm.id === member.id)
        : false,
    }));
  });

  // Initialize selectAll based on whether all members are checked
  const [selectAll, setSelectAll] = useState(() => {
    return (
      memberListWithCheckBox.length > 0 &&
      memberListWithCheckBox.every((member: any) => member.checked)
    );
  });

  // Update the member list when teamMembers prop changes
  useEffect(() => {
    if (teamMembers) {
      setMemberListWithCheckBox((prevMembers) =>
        prevMembers.map((member) => ({
          ...member,
          checked: teamMembers.some((tm: any) => tm.id === member.id),
        }))
      );

      // Update selectAll state
      const allChecked =
        members.length > 0 && members.length === teamMembers.length;
      setSelectAll(allChecked);
    }
  }, [teamMembers]);

  const toggleCheckbox = (id: string) => {
    setMemberListWithCheckBox((prevMembers: any) => {
      const updatedMembers = prevMembers.map((member: any) =>
        member.id === id || member._id === id
          ? { ...member, checked: !member.checked }
          : member
      );

      // If any item is unchecked, set Select All to false
      const allChecked = updatedMembers.every((member: any) => member.checked);
      setSelectAll(allChecked);

      // Update the parent component's teamMembers state
      const selectedMembers = updatedMembers.filter(
        (member: any) => member.checked
      );
      setTeamMembers(selectedMembers);

      return updatedMembers;
    });
  };

  const toggleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);

    setMemberListWithCheckBox((prevMembers: any) => {
      const updatedMembers = prevMembers.map((member: any) => ({
        ...member,
        checked: newSelectAll,
      }));

      // Update the parent component's teamMembers state
      const selectedMembers = newSelectAll ? [...updatedMembers] : [];
      setTeamMembers(selectedMembers);

      return updatedMembers;
    });
  };

  const renderItem = ({ item }: any) => (
    <TouchableOpacity
      style={styles.memberItem}
      onPress={() => toggleCheckbox(item.id || item._id)}
      activeOpacity={0.8}
    >
      <View
        style={[
          styles.checkbox,
          item.checked && { backgroundColor: colors.primaryBase },
        ]}
      >
        {item.checked && (
          <CustomIcon Icon={ICONS.CheckRightIcon} height={12} width={12} />
        )}
      </View>
      <Image
        source={{ uri: item.image || item.profilePicture }}
        style={{ width: 40, height: 40, marginRight: 10, borderRadius: 100 }}
      />
      <View style={styles.memberInfo}>
        <Text style={styles.memberName}>{item.name}</Text>
        <Text style={styles.memberEmail}>{item.email}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.header}>Team members</Text>
      <Text style={styles.subHeader}>
        Select team members who can perform this service
      </Text>

      {/* Select All Option */}
      <TouchableOpacity
        style={{
          flexDirection: "row",
          alignItems: "center",
          gap: moderateScale(5),
          marginVertical: moderateScale(15),
          paddingLeft: 12,
        }}
        onPress={toggleSelectAll}
      >
        <View
          style={[
            styles.checkbox,
            selectAll && { backgroundColor: colors.primaryBase },
          ]}
        >
          {selectAll && (
            <CustomIcon Icon={ICONS.CheckRightIcon} height={12} width={12} />
          )}
        </View>
        <Text
          style={{ fontSize: 12, fontWeight: "400", color: colors.greyText }}
        >
          ALL TEAM MEMBERS
        </Text>
      </TouchableOpacity>

      {/* Team Members List */}
      {memberListWithCheckBox.length > 0 ? (
        <FlatList
          data={memberListWithCheckBox}
          keyExtractor={(item) => item._id || item.id}
          renderItem={renderItem}
        />
      ) : (
        <Text style={styles.emptyText}>No team members available</Text>
      )}
    </View>
  );
};

export default ServicesTeam;

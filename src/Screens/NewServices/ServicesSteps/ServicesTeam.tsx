import React, { useEffect, useState } from "react";
import { FlatList, Image, Text, TouchableOpacity, View } from "react-native";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { TeamMember } from "../../../Services/ApiResponse";
import { moderateScale } from "../../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedServicesTeamStyles } from "./themedServicesTeamStyles";

const ServicesTeam = ({ teamMembers, setTeamMembers }: any) => {
  const { colors } = useTheme();
  const styles = useThemedServicesTeamStyles();

  // Initialize the member list with checkboxes
  // If a member is in the teamMembers array, mark it as checked
  const [memberListWithCheckBox, setMemberListWithCheckBox] = useState<
    Array<TeamMember & { checked: boolean }>
  >([]);

  // Initialize selectAll based on whether all members are checked
  const [selectAll, setSelectAll] = useState(() => {
    return (
      memberListWithCheckBox.length > 0 &&
      memberListWithCheckBox.every((member: any) => member.checked)
    );
  });

  // Update the member list when teamMembers prop changes
  useEffect(() => {
    const updatedMembers = teamMembers.map((member: any) => ({
      ...member,
      checked: false, // Default to checked, adjust as needed
    }));
    setMemberListWithCheckBox(updatedMembers);
    setSelectAll(
      updatedMembers.length > 0 &&
        updatedMembers.every((member: any) => member.checked)
    );
  }, [teamMembers]);

  const toggleCheckbox = (id: string) => {
    setMemberListWithCheckBox((prevMembers) => {
      const updatedMembers = prevMembers.map((member) =>
        member._id === id ? { ...member, checked: !member.checked } : member
      );

      // Update selectAll based on whether all members are checked
      const allChecked = updatedMembers.every((member) => member.checked);
      setSelectAll(allChecked);

      // Update parent component with selected members
      const selectedMembers = updatedMembers
        .filter((member) => member.checked)
        .map(({ checked, ...member }) => member); // Remove `checked` from parent state
      setTeamMembers(selectedMembers);

      return updatedMembers;
    });
  };

  const toggleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);

    setMemberListWithCheckBox((prevMembers) => {
      const updatedMembers = prevMembers.map((member) => ({
        ...member,
        checked: newSelectAll,
      }));

      // Update parent component with selected members
      const selectedMembers = newSelectAll
        ? updatedMembers.map(({ checked, ...member }) => member)
        : [];
      setTeamMembers(selectedMembers);

      return updatedMembers;
    });
  };

  const renderItem = ({
    item,
  }: {
    item: TeamMember & { checked: boolean };
  }) => (
    <TouchableOpacity
      style={styles.memberItem}
      onPress={() => toggleCheckbox(item._id)}
      activeOpacity={0.8}
    >
      <View
        style={[
          styles.checkbox,
          item.checked && { backgroundColor: colors.primaryBase },
        ]}
      >
        {item.checked && (
          <CustomIcon Icon={ICONS.CheckRightIcon} height={12} width={12} />
        )}
      </View>
      <Image
        source={{ uri: item.profilePicture }}
        style={{ width: 40, height: 40, marginRight: 10, borderRadius: 100 }}
      />
      <View style={styles.memberInfo}>
        <Text style={styles.memberName}>{item.name}</Text>
        <Text style={styles.memberEmail}>{item.email}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.header}>Team members</Text>
      <Text style={styles.subHeader}>
        Select team members who can perform this service
      </Text>

      {/* Select All Option */}
      <TouchableOpacity
        style={{
          flexDirection: "row",
          alignItems: "center",
          gap: moderateScale(5),
          marginVertical: moderateScale(15),
          paddingLeft: 12,
        }}
        onPress={toggleSelectAll}
      >
        <View
          style={[
            styles.checkbox,
            selectAll && { backgroundColor: colors.primaryBase },
          ]}
        >
          {selectAll && (
            <CustomIcon Icon={ICONS.CheckRightIcon} height={12} width={12} />
          )}
        </View>
        <Text
          style={{ fontSize: 12, fontWeight: "400", color: colors.greyText }}
        >
          ALL TEAM MEMBERS
        </Text>
      </TouchableOpacity>

      {/* Team Members List */}
      {memberListWithCheckBox.length > 0 ? (
        <FlatList
          data={memberListWithCheckBox}
          keyExtractor={(item) => item._id}
          renderItem={renderItem}
        />
      ) : (
        <Text style={styles.emptyText}>No team members available</Text>
      )}
    </View>
  );
};

export default ServicesTeam;

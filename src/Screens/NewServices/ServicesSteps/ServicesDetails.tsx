import React, { useState } from "react";
import {
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Modal,
  FlatList,
} from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import CustomIcon from "../../../Components/CustomIcon";
import ICONS from "../../../Assets/Icon";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedCommonStyles } from "../../../Utilities/Styles/themedCommonStyles";
import { useAppSelector } from "../../../Redux/store";
import { useThemedServicesDetailsStyles } from "./themedServicesDetailsStyles";

// Define types for Dropdown props
interface DropdownProps {
  visible: boolean;
  options: {
    id: string;
    name: string;
    description: string;
  }[];
  onSelect: (value: string) => void;
  onClose: () => void;
}

const Dropdown = ({ visible, options, onSelect, onClose }: DropdownProps) => {
  const styles = useThemedServicesDetailsStyles();

  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View style={styles.dropdownContainer}>
          <FlatList
            data={options}
            keyExtractor={(item, index) => index.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.dropdownItem}
                onPress={() => onSelect(item.id)}
              >
                <Text style={styles.dropdownText}>{item.name}</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const ServicesDetails = ({
  description,
  setDescription,
  businessName,
  setBusinessName,
  category,
  setCategory,
}: any) => {
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const { colors } = useTheme();
  const styles = useThemedServicesDetailsStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const { categories } = useAppSelector((state) => state.categories);
  const categoryOptions = categories.map((category) => category);

  const openDropdown = () => {
    setDropdownVisible(true);
  };

  const handleSelect = (value: string) => {
    setCategory(value);
    setDropdownVisible(false);
  };

  return (
    <View style={styles.container}>
      <Dropdown
        visible={dropdownVisible}
        options={categoryOptions}
        onSelect={handleSelect}
        onClose={() => setDropdownVisible(false)}
      />
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>
            Service name <Text style={styles.required}>*</Text>
          </Text>
          <TextInput
            style={styles.input}
            placeholder="e.g King's Specialty Haircut"
            placeholderTextColor={colors.greyText}
            value={businessName}
            onChangeText={setBusinessName}
            keyboardType="default"
          />
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>
            Category <Text style={styles.required}>*</Text>
          </Text>
          <TouchableOpacity
            style={styles.touchable}
            activeOpacity={0.8}
            onPress={openDropdown}
          >
            <Text
              style={[
                styles.input,
                category ? styles.selectedText : styles.placeholderText,
              ]}
            >
              {categories.filter((c) => c.id === category)[0]?.name ||
                "Select a category"}
            </Text>
            <CustomIcon Icon={ICONS.DropdownIcon} height={12} width={12} />
          </TouchableOpacity>
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.input, { height: 198, textAlignVertical: "top" }]}
            placeholder="Explain your service in more detail..."
            placeholderTextColor={colors.greyText}
            multiline
            value={description}
            maxLength={300}
            onChangeText={setDescription}
          />
          <View style={styles.resizetext}>
            <Text style={styles.charCount}>{description.length}/300</Text>
            <CustomIcon Icon={ICONS.ResizeText} height={12} width={12} />
          </View>
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
};

export default ServicesDetails;

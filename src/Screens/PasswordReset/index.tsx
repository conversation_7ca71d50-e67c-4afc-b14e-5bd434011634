import React, { useState } from "react";
import { Text, TextInput, TouchableOpacity, View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import { isValidEmail } from "../../Utilities/Helpers";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";
import { postData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import { ResetPasswordScreenProps } from "../../Navigation/Typings";
import useTranslation from "../../Localization/useTranslation";
import { useLanguage } from "../../Localization/LanguageContext";

const PasswordReset = ({ navigation }: ResetPasswordScreenProps) => {
  const [email, setEmail] = useState("");

  // Get theme context
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  // Get translation context
  const { auth, common } = useTranslation();
  const { currentLanguage, changeLanguage } = useLanguage();
  const [showLanguageSelector, setShowLanguageSelector] = useState(false);

  const handleSendResetLink = async () => {
    if (!isValidEmail(email)) {
      Toast.show({
        type: "error",
        text1: auth("pleaseEnterValidEmail"),
      });
      return;
    }

    try {
      const response = await postData<{ resetToken: string }>(
        ENDPOINTS.resetPassword,
        {
          email,
        }
      );

      if (response.data.success) {
        navigation.navigate("VerificationCode", {
          token: response.data.data.resetToken,
          email: email,
          isFrom: "reset",
        });

        Toast.show({
          type: "success",
          text1: response.data.message,
        });
      }
    } catch (error: any) {
      console.error("Error during password reset:", error);
      Toast.show({
        type: "error",
        text1: error.message || common("somethingWentWrong"),
      });
    }
  };

  return (
    <View style={styles.mainContainer}>
      <SafeAreaView
        style={{
          flex: 1,
          paddingVertical: verticalScale(20),
          paddingHorizontal: moderateScale(20),
        }}
      >
        {/* Language Selector */}
        <View style={styles.languageSelector}>
          <TouchableOpacity
            style={styles.languageButton}
            onPress={() => setShowLanguageSelector(!showLanguageSelector)}
          >
            <Text style={styles.languageText}>
              {currentLanguage === "en" ? "EN" : "SQ"}
            </Text>
            <CustomIcon Icon={ICONS.DropdownIcon} height={8} width={8} />
          </TouchableOpacity>

          {showLanguageSelector && (
            <View style={styles.languageDropdown}>
              <TouchableOpacity
                style={styles.languageOption}
                onPress={() => {
                  changeLanguage("en");
                  setShowLanguageSelector(false);
                }}
              >
                <Text style={styles.languageOptionText}>English</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.languageOption}
                onPress={() => {
                  changeLanguage("sq");
                  setShowLanguageSelector(false);
                }}
              >
                <Text style={styles.languageOptionText}>Shqip</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        <KeyboardAwareScrollView
          bounces={false}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.centeredView}>
            <CustomIcon Icon={ICONS.ResetpasswordIcon} height={96} width={96} />
            <Text
              style={[
                themedCommonStyles.font24W500,
                { marginTop: verticalScale(10) },
              ]}
            >
              {auth("resetPassword")}
            </Text>
            <Text
              style={[
                themedCommonStyles.font16400,
                { marginTop: verticalScale(2), textAlign: "center" },
              ]}
            >
              {auth("enterEmailToReset")}
            </Text>
          </View>

          <View style={{ marginTop: verticalScale(30) }}>
            {/* Email Input */}
            <Text style={styles.labelText}>
              {auth("emailAddress")}
              <Text style={{ color: colors.stateerrorbase }}>*</Text>
            </Text>
            <View style={styles.inputContainer}>
              <CustomIcon Icon={ICONS.EmailIcon} width={15} height={14.17} />
              <TextInput
                style={styles.input}
                placeholder={auth("enterEmailAddress")}
                placeholderTextColor={colors.greyText}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
              />
            </View>
          </View>

          <TouchableOpacity
            disabled={!isValidEmail(email)}
            style={[
              styles.loginButton,
              {
                backgroundColor: !isValidEmail(email)
                  ? colors.bgsoft
                  : colors.primaryBase,
              },
            ]}
            activeOpacity={0.8}
            onPress={handleSendResetLink}
          >
            <Text style={styles.loginText}>{auth("sendResetLink")}</Text>
          </TouchableOpacity>
        </KeyboardAwareScrollView>
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>
            {auth("changedYourMind")}
            {` `}
            <Text
              style={styles.linkTextPrimary}
              onPress={() => {
                navigation.navigate("LoginScreen");
              }}
            >
              {auth("goBack")}
            </Text>
          </Text>
        </View>
      </SafeAreaView>
    </View>
  );
};

export default PasswordReset;

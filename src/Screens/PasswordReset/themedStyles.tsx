import { StyleSheet, Platform } from 'react-native';
import { moderateScale } from '../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../Utilities/ThemeContext';

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    mainContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    safeAreaContainer: {
      flex: 1,
      paddingHorizontal: moderateScale(20),
    },
    centeredView: {
      alignItems: "center",
    },
    inputContainer: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      padding: Platform.OS == "ios" ? 10 : 0,
      paddingHorizontal: 10,
      marginBottom: moderateScale(15),
      marginTop: 5,
      width: "100%",
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    input: {
      flex: 1,
      marginHorizontal: moderateScale(10),
      color: colors.text,
    },
    loginButton: {
      backgroundColor: colors.bgsoft,
      padding: 10,
      borderRadius: 10,
      alignItems: "center",
      marginTop: 10,
    },
    loginText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
    titleText: {
      color: colors.text,
      fontSize: 24,
      fontWeight: "500",
      textAlign: "center",
    },
    subtitleText: {
      color: colors.text,
      fontSize: 16,
      fontWeight: "400",
    },
    labelText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    footerContainer: {
      alignItems: "center",
    },
    footerText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    linkTextPrimary: {
      color: colors.primaryBase,
      fontSize: 14,
      fontWeight: "500",
      textDecorationLine: "underline",
    },
    languageSelector: {
      position: "absolute",
      top: 10,
      right: 10,
      zIndex: 1000,
    },
    languageButton: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 8,
      gap: 8,
    },
    languageText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    languageDropdown: {
      position: "absolute",
      top: "100%",
      right: 0,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      marginTop: 4,
      minWidth: 100,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    languageOption: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    languageOptionText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "400",
    },
  });
};

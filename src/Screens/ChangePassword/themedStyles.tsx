import { StyleSheet } from "react-native";
import {
    moderateScale,
    verticalScale
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();

  return StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(20),
      backgroundColor: colors.background,
    },
  });
};

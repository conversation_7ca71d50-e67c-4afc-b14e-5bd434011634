import { Platform, StyleSheet } from "react-native";
import {
  moderateScale,
  moderateScaleVertical,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();

  return StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(20),
      backgroundColor: colors.background,
    },
    formContainer: {
      paddingHorizontal: moderateScale(10),
      paddingTop: verticalScale(20),
    },
    inputGroup: {
      marginBottom: verticalScale(20),
    },
    labelText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
      marginBottom: verticalScale(8),
    },
    requiredText: {
      color: colors.stateerrorbase,
    },
    inputContainer: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 10,
      paddingVertical: Platform.OS === "ios" ? 12 : 8,
      paddingHorizontal: moderateScale(12),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    input: {
      flex: 1,
      fontSize: 14,
      fontWeight: "400",
      color: colors.text,
      marginHorizontal: moderateScale(10),
      paddingVertical: 0, // Remove default padding
    },
    eyeButton: {
      padding: moderateScale(4),
    },
    requirementsContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(8),
      marginBottom: verticalScale(30),
      paddingHorizontal: moderateScale(4),
    },
    requirementsText: {
      fontSize: 12,
      fontWeight: "400",
      color: colors.greyText,
      flex: 1,
    },
    changePasswordButton: {
      borderRadius: 10,
      paddingVertical: moderateScaleVertical(14),
      alignItems: "center",
      justifyContent: "center",
      marginTop: verticalScale(10),
    },
    changePasswordButtonText: {
      fontSize: 14,
      fontWeight: "500",
    },
    warningContainer: {
      flexDirection: "row",
      gap: moderateScale(5),
      alignItems: "center",
    },
    warningText: {
      fontSize: 12,
      fontWeight: "400",
      color: colors.greyText,
    },
    passwordmatchingline: {
      width: 95,
      height: 4,
      backgroundColor: colors.stateerrorbase,
      borderRadius: 1.2,
    },
    passwordmatchingline1: {
      width: 95,
      height: 4,
      backgroundColor: colors.statewarningbase,
      borderRadius: 1.2,
      marginHorizontal: 10,
    },
    passwordmatchingline2: {
      width: 95,
      height: 4,
      backgroundColor: colors.statesuccessbase,
      borderRadius: 1.2,
    },
    validationText: {
      marginTop: 5,
      fontSize: 12,
      fontWeight: "400",
      color: colors.text,
    },
  });
};

import { ScrollView, StyleSheet, Text, View } from "react-native";
import React, { FC } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { useThemedStyles } from "./themedStyles";
import useTranslation from "../../Localization/useTranslation";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { ChangePasswordProps } from "../../Navigation/Typings";

const ChangePassword: FC<ChangePasswordProps> = ({ navigation }) => {
  const styles = useThemedStyles();
  const { changePassword, common } = useTranslation();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <UnifiedHeader
          title={changePassword("changePassword")}
          onBackPress={() => navigation.goBack()}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default ChangePassword;

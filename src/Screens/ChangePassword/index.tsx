import React, { FC, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import useTranslation from "../../Localization/useTranslation";
import { ChangePasswordProps } from "../../Navigation/Typings";
import { postData, putData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import { useTheme } from "../../Utilities/ThemeContext";

import { useThemedStyles } from "./themedStyles";
import { verticalScale } from "../../Utilities/Styles/responsiveSize";

const ChangePassword: FC<ChangePasswordProps> = ({ navigation }) => {
  const styles = useThemedStyles();
  const { colors } = useTheme();
  const { changePassword, common } = useTranslation();

  // State for form inputs
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmNewPassword, setShowConfirmNewPassword] = useState(false);
  const [hasUppercase, setHasUppercase] = useState(false);
  const [hasNumber, setHasNumber] = useState(false);
  const [hasMinLength, setHasMinLength] = useState(false);

  const [isLoading, setIsLoading] = useState(false);

  // Validation
  const isFormValid =
    currentPassword.trim().length > 0 && newPassword.trim().length >= 6;

  const handleChangePassword = async () => {
    const trimmedCurrentPassword = currentPassword.trim();
    const trimmedNewPassword = newPassword.trim();
    const trimmedConfirmNewPassword = confirmNewPassword.trim();

    if (trimmedCurrentPassword === trimmedNewPassword) {
      Toast.show({
        type: "error",
        text1: "New password must be different from current password",
      });
      return;
    }

    if (newPassword !== confirmNewPassword) {
      Toast.show({
        type: "error",
        text1: "New Password and Confirm New Password do not match",
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await putData(ENDPOINTS.updatePassword, {
        currentPassword: trimmedCurrentPassword,
        newPassword: trimmedConfirmNewPassword,
      });

      if (response.data.success) {
        Toast.show({
          type: "success",
          text1: changePassword("passwordChanged"),
        });

        // Clear form   
        setCurrentPassword("");
        setNewPassword("");
        setConfirmNewPassword("");

        // Navigate back
        navigation.goBack();
      }
    } catch (error: any) {
      console.error("Error changing password:", error);
      Toast.show({
        type: "error",
        text1: error.message || "Failed to change password. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    setHasUppercase(/[A-Z]/.test(newPassword));
    setHasNumber(/\d/.test(newPassword));
    setHasMinLength(newPassword.length >= 8);
  }, [newPassword]);

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <UnifiedHeader
          title={changePassword("changePassword")}
          onBackPress={() => navigation.goBack()}
        />

        <View style={styles.formContainer}>
          {/* Current Password Input */}
          <View style={styles.inputGroup}>
            <Text style={styles.labelText}>
              {changePassword("currentPassword")}
              <Text style={styles.requiredText}>*</Text>
            </Text>
            <View style={styles.inputContainer}>
              <CustomIcon Icon={ICONS.LockIcon} width={12} height={15} />
              <TextInput
                style={styles.input}
                placeholder="••••••••••"
                placeholderTextColor={colors.greyText}
                secureTextEntry={!showCurrentPassword}
                value={currentPassword}
                onChangeText={setCurrentPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                onPress={() => setShowCurrentPassword(!showCurrentPassword)}
                style={styles.eyeButton}
              >
                <CustomIcon
                  Icon={showCurrentPassword ? ICONS.EyeIcon : ICONS.EyeoffIcon}
                  width={16.23}
                  height={13.5}
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* New Password Input */}
          <View style={styles.inputGroup}>
            <Text style={styles.labelText}>
              {changePassword("newPassword")}
              <Text style={styles.requiredText}>*</Text>
            </Text>
            <View style={styles.inputContainer}>
              <CustomIcon Icon={ICONS.LockIcon} width={12} height={15} />
              <TextInput
                style={styles.input}
                placeholder="••••••••••"
                placeholderTextColor={colors.greyText}
                secureTextEntry={!showNewPassword}
                value={newPassword}
                onChangeText={setNewPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                onPress={() => setShowNewPassword(!showNewPassword)}
                style={styles.eyeButton}
              >
                <CustomIcon
                  Icon={showNewPassword ? ICONS.EyeIcon : ICONS.EyeoffIcon}
                  width={16.23}
                  height={13.5}
                />
              </TouchableOpacity>
            </View>
          </View>
          {/* Confirm New Password Input */}
          <View style={styles.inputGroup}>
            <Text style={styles.labelText}>
              {changePassword("confirmNewPassword")}
              <Text style={styles.requiredText}>*</Text>
            </Text>
            <View style={styles.inputContainer}>
              <CustomIcon Icon={ICONS.LockIcon} width={12} height={15} />
              <TextInput
                style={styles.input}
                placeholder="••••••••••"
                placeholderTextColor={colors.greyText}
                secureTextEntry={!showConfirmNewPassword}
                value={confirmNewPassword}
                onChangeText={setConfirmNewPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                onPress={() =>
                  setShowConfirmNewPassword(!showConfirmNewPassword)
                }
                style={styles.eyeButton}
              >
                <CustomIcon
                  Icon={showNewPassword ? ICONS.EyeIcon : ICONS.EyeoffIcon}
                  width={16.23}
                  height={13.5}
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Password Requirements */}
          <View style={styles.warningContainer}>
            <CustomIcon Icon={ICONS.WarningIcon} height={12} width={12} />
            <Text style={styles.warningText}>
              Must contain at least 1 uppercase letter, 1 number, min. 8
              characters.
            </Text>
          </View>

          {newPassword.length > 0 && (
            <View
              style={{
                paddingVertical: verticalScale(10),
                flexDirection: "row",
              }}
            >
              <View
                style={[
                  styles.passwordmatchingline,
                  {
                    backgroundColor: hasUppercase
                      ? colors.stateerrorbase
                      : colors.bgsoft,
                  },
                ]}
              />
              <View
                style={[
                  styles.passwordmatchingline1,
                  {
                    backgroundColor: hasNumber
                      ? colors.statewarningbase
                      : colors.bgsoft,
                  },
                ]}
              />
              <View
                style={[
                  styles.passwordmatchingline2,
                  {
                    backgroundColor: hasMinLength
                      ? colors.statesuccessbase
                      : colors.bgsoft,
                  },
                ]}
              />
            </View>
          )}

          {newPassword.length > 0 && (
            <View>
              <Text style={styles.validationText}>
                <CustomIcon
                  Icon={
                    hasUppercase ? ICONS.GreenrightIcon : ICONS.CrossvectorIcon
                  }
                  height={12}
                  width={12}
                />{" "}
                At least 1 uppercase
              </Text>
              <Text style={styles.validationText}>
                <CustomIcon
                  Icon={
                    hasNumber ? ICONS.GreenrightIcon : ICONS.CrossvectorIcon
                  }
                  height={12}
                  width={12}
                />{" "}
                At least 1 number
              </Text>
              <Text style={styles.validationText}>
                <CustomIcon
                  Icon={
                    hasMinLength ? ICONS.GreenrightIcon : ICONS.CrossvectorIcon
                  }
                  height={12}
                  width={12}
                />{" "}
                At least 8 characters
              </Text>
            </View>
          )}

          {/* Change Password Button */}
          <TouchableOpacity
            style={[
              styles.changePasswordButton,
              {
                backgroundColor:
                  isFormValid && !isLoading
                    ? colors.primaryBase
                    : colors.bgsoft,
              },
            ]}
            disabled={!isFormValid || isLoading}
            onPress={handleChangePassword}
            activeOpacity={0.8}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color={colors.white} />
            ) : (
              <Text
                style={[
                  styles.changePasswordButtonText,
                  {
                    color:
                      isFormValid && !isLoading
                        ? colors.white
                        : colors.greyText,
                  },
                ]}
              >
                {common("save")}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default ChangePassword;

import React, { useRef, useState } from "react";
import {
  ActivityIndicator,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";
import { VerificationCodeProps } from "../../Navigation/Typings";
import { postData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import Toast from "react-native-toast-message";
import useTranslation from "../../Localization/useTranslation";
import { useLanguage } from "../../Localization/LanguageContext";

const VerificationCode = ({ navigation, route }: VerificationCodeProps) => {
  const { isFrom, email, token } = route.params;

  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [isOtpValid, setIsOtpValid] = useState(true);
  const inputs = useRef<(TextInput | null)[]>([]);

  const [isResendLoading, setIsResendLoading] = useState(false);

  // Get theme context
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  // Get translation context
  const { auth, common } = useTranslation();
  const { currentLanguage, changeLanguage } = useLanguage();
  const [showLanguageSelector, setShowLanguageSelector] = useState(false);

  const isButtonEnabled = otp.join("").length === 6;

  const handleContinue = async () => {
    const enteredOtp = otp.join("");

    if (isFrom === "signup") {
      try {
        const response = await postData(ENDPOINTS.registerOtpVerify, {
          verificationToken: token,
          otp: enteredOtp,
          email,
        });
        if (response.data.success) {
          Toast.show({
            type: "success",
            text1: response.data.message,
          });
          navigation.navigate("LoginScreen");
        }
      } catch (error: any) {
        console.error("Error during registration:", error);
        Toast.show({
          type: "error",
          text1: error.message || common("somethingWentWrong"),
        });
      }
    } else {
      try {
        const response = await postData(ENDPOINTS.resetPasswordOtpVerify, {
          resetToken: token,
          otp: enteredOtp,
          email,
        });

        if (response.data.success) {
          Toast.show({
            type: "success",
            text1: response.data.message,
          });

          navigation.navigate("SetNewPassword", {
            email,
            resetToken: token,
          });
        }
      } catch (error: any) {
        console.error("Error during password reset:", error);
        Toast.show({
          type: "error",
          text1: error.message || common("somethingWentWrong"),
        });
      }
    }
  };

  const handleInputChange = (value: string, index: number) => {
    if (!/^\d*$/.test(value)) {
      Toast.show({
        type: "error",
        text1: auth("invalidOtp"),
        text2: auth("pleaseEnterOnlyNumbers"),
      });
      return; // Ignore input if it's not a number
    }
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value && index < 5) {
      inputs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (event: any, index: number) => {
    if (event.nativeEvent.key === "Backspace" && !otp[index] && index > 0) {
      inputs.current[index - 1]?.focus();
    }
  };

  return (
    <View style={styles.mainContainer}>
      <SafeAreaView
        style={{
          flex: 1,
          paddingVertical: verticalScale(20),
          paddingHorizontal: moderateScale(20),
        }}
      >
        {/* Language Selector */}
        <View style={styles.languageSelector}>
          <TouchableOpacity
            style={styles.languageButton}
            onPress={() => setShowLanguageSelector(!showLanguageSelector)}
          >
            <Text style={styles.languageText}>
              {currentLanguage === "en" ? "EN" : "SQ"}
            </Text>
            <CustomIcon Icon={ICONS.DropdownIcon} height={8} width={8} />
          </TouchableOpacity>

          {showLanguageSelector && (
            <View style={styles.languageDropdown}>
              <TouchableOpacity
                style={styles.languageOption}
                onPress={() => {
                  changeLanguage("en");
                  setShowLanguageSelector(false);
                }}
              >
                <Text style={styles.languageOptionText}>English</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.languageOption}
                onPress={() => {
                  changeLanguage("sq");
                  setShowLanguageSelector(false);
                }}
              >
                <Text style={styles.languageOptionText}>Shqip</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        <KeyboardAwareScrollView
          bounces={false}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.centeredView}>
            <CustomIcon Icon={ICONS.verificationIcon} height={96} width={96} />
            <Text
              style={[
                themedCommonStyles.font24W500,
                { marginTop: verticalScale(10) },
              ]}
            >
              {auth("enterVerificationCode")}
            </Text>
            <Text
              style={[
                themedCommonStyles.font16400,
                { marginTop: verticalScale(2) },
              ]}
            >
              {auth("weSentCodeTo")}
              <Text style={styles.emailText}> {email}</Text>
            </Text>
          </View>
          <View style={styles.inputContainer}>
            {otp.map((digit, index) => (
              <TextInput
                key={index}
                ref={(ref: any) => (inputs.current[index] = ref)}
                style={[
                  styles.input,
                  {
                    borderColor: isOtpValid
                      ? colors.border
                      : colors.stateerrorbase,
                  },
                ]}
                keyboardType="numeric"
                maxLength={1}
                value={digit}
                onChangeText={(value) => handleInputChange(value, index)}
                onKeyPress={(event) => handleKeyPress(event, index)}
                autoFocus={index === 0}
                placeholderTextColor={colors.greyText}
              />
            ))}
          </View>

          <TouchableOpacity
            style={[
              styles.loginButton,
              {
                backgroundColor: isButtonEnabled
                  ? colors.primaryBase
                  : colors.bgsoft,
              },
            ]}
            disabled={!isButtonEnabled}
            onPress={handleContinue}
            activeOpacity={0.8}
          >
            {isResendLoading ? (
              <ActivityIndicator size="small" color={colors.white} />
            ) : (
              <Text
                style={[
                  styles.loginText,
                  { color: isButtonEnabled ? colors.white : colors.greyText },
                ]}
              >
                {auth("submitCode")}
              </Text>
            )}
          </TouchableOpacity>

          {!isOtpValid ? (
            <View style={styles.centeredTextContainer}>
              <Text style={styles.errorText}>{auth("codeIsIncorrect")}</Text>
            </View>
          ) : (
            <View style={styles.centeredTextContainer}>
              <Text style={styles.helpText}>{auth("experiencingIssues")}</Text>
            </View>
          )}
          <Text style={styles.resetCodetxt}>{auth("resendCode")}</Text>
        </KeyboardAwareScrollView>
      </SafeAreaView>
    </View>
  );
};

export default VerificationCode;

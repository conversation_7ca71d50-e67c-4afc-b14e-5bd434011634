import { StyleSheet } from 'react-native';
import { moderateScale, verticalScale } from '../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../Utilities/ThemeContext';

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    mainContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    safeAreaContainer: {
      flex: 1,
      paddingHorizontal: moderateScale(20),
    },
    centeredView: {
      alignItems: "center",
    },
    inputContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
      gap: moderateScale(15),
      marginTop: verticalScale(30),
    },
    input: {
      flex: 1,
      borderRadius: 12,
      paddingHorizontal: moderateScale(15),
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
      height: 64,
      fontSize: 16,
      color: colors.text,
      textAlign: "center",
      borderWidth: 1,
      borderColor: colors.border,
    },
    loginButton: {
      backgroundColor: colors.bgsoft,
      padding: 10,
      borderRadius: 10,
      alignItems: "center",
      marginTop: verticalScale(20),
    },
    loginText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    resetCodetxt: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
      textDecorationLine: "underline",
      marginTop: 5,
      textAlign: "center",
    },
    titleText: {
      color: colors.text,
      fontSize: 24,
      fontWeight: "500",
      textAlign: "center",
    },
    subtitleText: {
      color: colors.text,
      fontSize: 16,
      fontWeight: "400",
    },
    emailText: {
      color: colors.text,
      fontSize: 16,
      fontWeight: "500",
    },
    errorText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    helpText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    centeredTextContainer: {
      alignItems: "center",
      marginTop: verticalScale(25),
    },
    languageSelector: {
      position: "absolute",
      top: 10,
      right: 10,
      zIndex: 1000,
    },
    languageButton: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 8,
      gap: 8,
    },
    languageText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    languageDropdown: {
      position: "absolute",
      top: "100%",
      right: 0,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      marginTop: 4,
      minWidth: 100,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    languageOption: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    languageOptionText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "400",
    },
  });
};

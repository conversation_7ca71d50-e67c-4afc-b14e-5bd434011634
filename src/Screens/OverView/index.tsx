import moment from "moment";
import React, { FC, useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icon";
import Calendar from "../../Components/Calendar";
import CustomIcon from "../../Components/CustomIcon";
import MonthDropdown from "../../Components/MonthDropdown";
import ViewModeDropdown from "../../Components/ViewModeDropdown";
import useNetworkStatus from "../../Hooks/useNetworkStatus";
import { OverViewProps } from "../../Navigation/Typings";
import {
  addAppointment,
  deleteAppointment,
  setAppointments,
  updateAppointment,
} from "../../Redux/slices/appointmentSlice";
import { setUser } from "../../Redux/slices/userSlice";
import { useAppDispatch, useAppSelector } from "../../Redux/store";
import {
  GetAllAppointmentsApiResponse,
  GetAllClientsApiResponse,
  GetAllTeamMembersApiResponse,
  GetAllUserCategoriesApiResponse,
  GetProfileApiResponse,
} from "../../Services/ApiResponse";
import { fetchData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import CalendarAppointmentView from "../../Utilities/Components/Modal/CalendarAppointmentView";
import TeamMemberModal from "../../Utilities/Components/Modal/TeamMember";
import {
  calculateDuration,
  getCurrentWeek,
  getMonthData,
} from "../../Utilities/Helpers";
import { verticalScale } from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";
import useTranslation from "../../Localization/useTranslation";
import { setMembers } from "../../Redux/slices/memberSlice";
import Toast from "react-native-toast-message";
import { setClients } from "../../Redux/slices/clientSlice";
import { setCategories } from "../../Redux/slices/categorySlice";

// Functions to determine appointment colors based on status
const getCardColor = (status: string) => {
  switch (status) {
    case "CANCELED":
      return "#FFEBEC"; // Light red for canceled
    case "CONFIRMED":
      return "#50fd9d"; // Green for confirmed
    default:
      return "#007AFF"; // Blue as default
  }
};

const getCardHeadingColor = (status: string) => {
  switch (status) {
    case "CANCELED":
      return "#FB3748"; // Red for canceled
    case "CONFIRMED":
      return "#0E121B"; // Black for confirmed
    default:
      return "#007AFF"; // Blue as default
  }
};

const getAccentColor = (status: string) => {
  switch (status) {
    case "CANCELED":
      return "#FB3748"; // Red for canceled
    case "CONFIRMED":
      return "#6E7B91"; // Dark gray for confirmed
    default:
      return "#6E7B91"; // Dark gray as default
  }
};

const OverViewScreen: FC<OverViewProps> = ({ navigation }) => {
  // Use the custom hook for network status
  const { isConnected, loading } = useNetworkStatus();
  const { colors, isDarkMode } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();
  const dispatch = useAppDispatch();
  const { navigation: navTranslation, calendar, common } = useTranslation();

  const [isModalVisible, setModalVisible] = useState(false);
  const [selectedDate, setSelectedDate] = useState(
    moment().format("YYYY-MM-DD")
  );

  const [viewMode, setViewMode] = useState<"Hourly" | "Weekly" | "Monthly">(
    "Weekly"
  );

  const [isViewModeDropdownVisible, setViewModeDropdownVisible] =
    useState(false);

  // Month selection state
  const [selectedMonth, setSelectedMonth] = useState(moment());
  const [isMonthDropdownVisible, setMonthDropdownVisible] = useState(false);

  // References to the FlatLists for scrolling
  const monthListRef = useRef<FlatList>(null);
  const weekListRef = useRef<FlatList>(null);

  // Get calendar data based on view mode
  const getCalendarData = () => {
    if (viewMode === "Weekly") {
      return getCurrentWeek();
    } else {
      // For monthly view, use the selected month
      return getMonthData(selectedMonth).days;
    }
  };

  // Effect to handle view mode changes and month selection
  React.useEffect(() => {
    // When view mode changes, we need to reset the scroll position
    if (viewMode === "Monthly" && monthListRef.current) {
      const monthData = getMonthData(selectedMonth);
      if (monthData.todayIndex >= 0) {
        const itemWidth = 60;
        const offset = Math.max(0, monthData.todayIndex * itemWidth - 120);

        setTimeout(() => {
          if (monthListRef.current) {
            monthListRef.current.scrollToOffset({
              offset,
              animated: true,
            });
          }
        }, 100);
      }
    } else if (viewMode === "Weekly" && weekListRef.current) {
      // For weekly view, we can also scroll to today if needed
      // This would be similar to the monthly view logic
      weekListRef.current.scrollToOffset({ offset: 0, animated: true });
    }
  }, [viewMode, selectedMonth]);

  const [selectedOption, setSelectedOption] = useState<
    "Calendar" | "My Appointments"
  >("Calendar");

  // Get appointments from Redux store
  const { appointments } = useAppSelector((state) => state.appointments);

  // Filter appointments based on view mode and selected date/month
  const appointmentsForSelectedDate = React.useMemo(() => {
    // Always filter by selectedDate for displaying appointments
    // The calendar view mode only affects how the calendar is rendered,
    // but the list of appointments should always correspond to the specific selected day.
    return appointments.filter((appointment) =>
      moment(selectedDate).isSame(moment(appointment.date), "day")
    );
  }, [appointments, selectedDate]); // Removed selectedMonth and viewMode from dependencies

  // Handle adding a new appointment
  const handleAddAppointment = (appointment: any) => {
    dispatch(addAppointment(appointment));
  };

  // Handle updating an appointment
  const handleUpdateAppointment = (appointment: any) => {
    dispatch(updateAppointment(appointment));
  };

  // Handle deleting an appointment
  const handleDeleteAppointment = (id: string) => {
    dispatch(deleteAppointment(id));
  };

  const upcomingAppointments = appointments
    .filter((appointment) => {
      const appointmentDateTime = moment(
        `${appointment.date} ${appointment.startTime}`,
        "YYYY-MM-DD HH:mm"
      );
      const currentDateTime = moment();
      return (
        appointmentDateTime.isAfter(currentDateTime) &&
        appointment.status !== "CANCELED"
      );
    })
    .sort((a, b) => {
      const dateA = moment(`${a.date} ${a.startTime}`, "YYYY-MM-DD HH:mm");
      const dateB = moment(`${b.date} ${b.startTime}`, "YYYY-MM-DD HH:mm");
      return dateA.diff(dateB);
    });

  const renderAppointmentItem = ({
    item,
  }: {
    item: (typeof appointments)[0];
  }) => (
    <View
      style={[
        styles.appointmentItem,
        { backgroundColor: getCardColor(item.status) },
      ]}
    >
      <View
        style={[
          styles.accentLine,
          { backgroundColor: getAccentColor(item.status) },
        ]}
      />
      <View style={styles.timeContainer}>
        <Text style={styles.timeText}>{item.startTime}</Text>
      </View>
      <View style={styles.detailsContainer}>
        <Text
          style={[
            styles.clientName,
            { color: getCardHeadingColor(item.status) },
          ]}
        >
          {item.clientName}
        </Text>
        <Text style={styles.serviceText}>{item.service}</Text>
        <Text style={styles.durationText}>
          {`${item.startTime} - ${item.endTime} (${calculateDuration(
            item.startTime,
            item.endTime
          )})`}
        </Text>
        <Text style={styles.teamMemberText}>{item.teamMember}</Text>
      </View>
    </View>
  );

  useEffect(() => {
    const getUserData = async () => {
      try {
        const response = await fetchData<GetProfileApiResponse>(
          ENDPOINTS.getUserProfileData
        );

        if (response.data.success) {
          dispatch(setUser(response.data.data.profile));
        }
      } catch (error: any) {
        console.error("Error during password reset:", error);
      }
      return null;
    };

    const fetchAppointments = async () => {
      try {
        const response = await fetchData<GetAllAppointmentsApiResponse>(
          `${ENDPOINTS.getAllAppointmentsByDate}?date=${moment().format(
            "YYYY-MM-DD"
          )}`
        );

        if (response.data.success) {
          dispatch(
            setAppointments(
              response.data.data.appointments.map((apt) => ({
                id: apt._id,
                clientId: apt.clientId,
                clientName: apt.clientName,
                date: apt.date,
                startTime: apt.startTime,
                endTime: apt.endTime,
                title: apt.clientName,
                status: apt.status,
                service: apt.services[0].name,
                teamMember: apt.teamMemberName,
                teamMemberId: apt.teamMemberId,
              }))
            )
          );
        }
      } catch (error: any) {
        console.error("Error fetching appointments:", error);
      }
    };
    fetchAppointments();
    getUserData();
  }, []);

  const getCategories = async () => {
    try {
      const response = await fetchData<GetAllUserCategoriesApiResponse>(
        ENDPOINTS.getAlluserCategories
      );
      if (response.data.success) {
        dispatch(
          setCategories(
            response.data.data.categories.map((cat) => ({
              id: cat._id,
              name: cat.name,
              description: cat.description,
            }))
          )
        );
      }
    } catch (error: any) {
      console.error("Error fetching categories:", error);
      Toast.show({
        type: "error",
        text1: "Something went wrong. Please try again later.",
      });
    }
  };

  const getClients = async () => {
    try {
      const response = await fetchData<GetAllClientsApiResponse>(
        ENDPOINTS.getAllClients
      );
      if (response.data.success) {
        dispatch(
          setClients(
            response.data.data.clients.map((client) => ({
              id: client._id,
              name: client.name,
              email: client.email,
              number: client.phoneNumber,
              countryCode: client.countryCode,
              countryCallingCode: client.countryCallingCode,
              image: client.profilePicture,
              birthday: client.birthday,
              gender: client.gender,
            }))
          )
        );
      }
    } catch (error: any) {
      console.error("Error fetching categories:", error);
      Toast.show({
        type: "error",
        text1: "Something went wrong. Please try again later.",
      });
    } finally {
    }
  };

  const getAllTeammembers = async () => {
    try {
      const response = await fetchData<GetAllTeamMembersApiResponse>(
        ENDPOINTS.getAllTeamMembers
      );
      if (response.data.success) {
        dispatch(
          setMembers(
            response.data.data.teamMembers.map((member) => ({
              id: member._id,
              name: member.name,
              email: member.email,
              number: member.phoneNumber,
              countryCode: member.countryCode,
              callingCode: member.countryCallingCode,
              image: member.profilePicture,
              checked: false,
            }))
          )
        );
      }
    } catch (error: any) {
      console.error("Error fetching team members:", error);
      Toast.show({
        type: "error",
        text1: error.message || "Something went wrong. Please try again later.",
      });
    }
  };

  useEffect(() => {
    getCategories();
    getClients();
    getAllTeammembers();
  }, []);

  return (
    <SafeAreaView
      edges={["top", "left", "right"]}
      style={styles.safeAreaContainer}
    >
      {/* Top Header - Always Visible */}
      <View style={styles.headerRow}>
        <CustomIcon
          Icon={isDarkMode ? ICONS.WhiteLogo : ICONS.GlamupLogoIcon}
          height={28}
          width={111}
        />
        {isConnected && (
          <View style={styles.iconRow}>
            <TouchableOpacity
              style={styles.viewModeButton}
              onPress={() => setViewModeDropdownVisible(true)}
            >
              <Text style={styles.viewModeText}>{viewMode}</Text>
              <CustomIcon Icon={ICONS.DropdownIcon} height={8} width={8} />
            </TouchableOpacity>

            {/* View Mode Dropdown */}
            <ViewModeDropdown
              isVisible={isViewModeDropdownVisible}
              onClose={() => setViewModeDropdownVisible(false)}
              currentMode={viewMode}
              onSelectMode={setViewMode}
            />

            {/* Month Dropdown Component */}
            <MonthDropdown
              isVisible={isMonthDropdownVisible}
              onClose={() => setMonthDropdownVisible(false)}
              currentMonth={selectedMonth}
              onSelectMonth={(month: moment.Moment) => {
                setSelectedMonth(month); // Update the selected month
                let newSelectedDate = moment(month)
                  .startOf("month")
                  .format("YYYY-MM-DD"); // Default to the first day of the selected month
                // If the newly selected month is the *current* month, try to select today's date within it.
                // Otherwise, stick with the first day of the selected month.
                if (moment().isSame(month, "month")) {
                  newSelectedDate = moment().format("YYYY-MM-DD");
                }
                setSelectedDate(newSelectedDate); // Update the selected date
              }}
            />

            <TouchableOpacity onPress={() => setModalVisible(true)}>
              <CustomIcon
                Icon={ICONS.OverviewteamMember}
                height={20}
                width={20}
              />
              {isModalVisible && (
                <TeamMemberModal
                  visible={isModalVisible}
                  onClose={() => setModalVisible(false)}
                  onPressMember={(item: any) => {
                    setModalVisible(false);
                    navigation.navigate("addTeamMember", {
                      memberData: item,
                    });
                  }}
                />
              )}
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Loading Indicator */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.BaseColor} />
          <Text style={themedCommonStyles.font16medium}>
            {calendar("checkingNetwork")}
          </Text>
        </View>
      ) : !isConnected ? (
        /* No Internet UI */
        <View style={styles.noInternetContainer}>
          <CustomIcon
            Icon={ICONS.NoInternetConnectIcon}
            width={verticalScale(100)}
            height={verticalScale(100)}
          />
          <Text style={themedCommonStyles.font16medium}>
            {calendar("noInternetConnection")}
          </Text>
          <Text
            style={[themedCommonStyles.font14greytext, { textAlign: "center" }]}
          >
            {calendar("quietDays")}
          </Text>
          <TouchableOpacity style={styles.tryAgainButton}>
            <CustomIcon Icon={ICONS.SearchIcon} height={15} width={15} />
            <Text style={styles.tryAgainText}>{calendar("tryAgain")}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          {/* Month Dropdown for Monthly View */}
          {viewMode === "Monthly" && (
            <TouchableOpacity
              style={styles.monthDropdownButton}
              onPress={() => setMonthDropdownVisible(true)}
            >
              <Text style={styles.monthDropdownText}>
                {selectedMonth.format("MMM YYYY")}
              </Text>
              <CustomIcon Icon={ICONS.DropdownIcon} height={8} width={8} />
            </TouchableOpacity>
          )}
          {/* Week Navigation */}
          {viewMode !== "Hourly" && (
            <View style={styles.weekNavigation}>
              {/* Calendar View (Weekly or Monthly) */}
              <FlatList
                ref={viewMode === "Monthly" ? monthListRef : weekListRef}
                style={{ flex: 1 }}
                onLayout={() => {
                  // Auto-scroll to today when the component mounts
                  if (viewMode === "Monthly") {
                    const monthData = getMonthData(selectedMonth);
                    if (monthData.todayIndex >= 0 && monthListRef.current) {
                      // Calculate the offset to center the current day
                      // Assuming each day item has a width of about 50-60 pixels
                      const itemWidth = 60;
                      const offset = Math.max(
                        0,
                        monthData.todayIndex * itemWidth - 120
                      );

                      // Scroll to the position with animation
                      setTimeout(() => {
                        if (monthListRef.current) {
                          monthListRef.current.scrollToOffset({
                            offset,
                            animated: true,
                          });
                        }
                      }, 100);
                    }
                  }
                }}
                horizontal
                showsHorizontalScrollIndicator={false}
                data={getCalendarData()}
                keyExtractor={(item) => item.formattedDate}
                renderItem={({ item }) => {
                  return (
                    <TouchableOpacity
                      key={item.formattedDate}
                      style={styles.dayContainer}
                      onPress={() => setSelectedDate(item.formattedDate)}
                      disabled={item.isFaded}
                    >
                      <View style={styles.dayWrapper}>
                        <Text
                          style={[
                            styles.dayText,
                            item.isFaded && { opacity: 0.4 },
                          ]}
                        >
                          {item.date.format("ddd")}
                        </Text>
                        <View
                          style={[
                            styles.dateWrapper,
                            (item.formattedDate === selectedDate ||
                              item.isToday) &&
                              styles.selectedDay,
                            // Add a special style for today
                            item.isToday && {
                              backgroundColor: colors.primaryBase,
                              borderWidth: 2,
                              borderColor: colors.stateerrorbase,
                            },
                          ]}
                        >
                          <Text
                            style={[
                              styles.dateText,
                              item.isFaded && { opacity: 0.4 },
                              (item.formattedDate === selectedDate ||
                                item.isToday) && {
                                color: "white",
                              },
                            ]}
                          >
                            {item.date.format("D")}
                          </Text>
                        </View>
                      </View>
                    </TouchableOpacity>
                  );
                }}
              />
            </View>
          )}

          {selectedOption === "Calendar" ? (
            <Calendar
              data={appointmentsForSelectedDate}
              selectedDate={selectedDate}
              onAddAppointment={handleAddAppointment}
              onUpdateAppointment={handleUpdateAppointment}
              onDeleteAppointment={handleDeleteAppointment}
              viewMode={viewMode}
            />
          ) : (
            <FlatList
              data={appointmentsForSelectedDate}
              renderItem={renderAppointmentItem}
              keyExtractor={(item) => item.id}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>
                    No appointments for{" "}
                    {moment(selectedDate).format("MMM D, YYYY")}
                  </Text>
                </View>
              }
              contentContainerStyle={styles.listContent}
            />
          )}

          <CalendarAppointmentView
            selectedOption={selectedOption}
            setSelectedOption={setSelectedOption}
            upcomingAppointments={upcomingAppointments.length}
          />
        </>
      )}
    </SafeAreaView>
  );
};

export default OverViewScreen;

import React, { useState } from "react";
import {
  <PERSON>ert,
  FlatList,
  Image,
  ImageBackground,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import { SafeAreaView } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import LanguageSelector from "../../Components/LanguageSelector";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { SettingsProps } from "../../Navigation/Typings";
import { setIsAuth } from "../../Redux/slices/initialSlice";
import { useAppDispatch, useAppSelector } from "../../Redux/store";
import ImagePath from "../../Utilities/Constants/ImagePath";
import { deleteLocalStorageData } from "../../Utilities/Helpers";
import STORAGE_KEYS from "../../Utilities/StorageKeys";
import fontFamily from "../../Utilities/Styles/fontFamily";
import { textScale } from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { ThemeType, useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";
import useTranslation from "../../Localization/useTranslation";
import ENDPOINTS from "../../Services/EndPoints";
import { postData } from "../../Services/ApiService";

// Define options

// Define types for Dropdown props
interface DropdownProps {
  visible: boolean;
  options: string[];
  onSelect: (value: string) => void;
  onClose: () => void;
}

const Dropdown = ({ visible, options, onSelect, onClose }: DropdownProps) => {
  const { colors } = useTheme();

  const dropdownStyles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: "rgba(0,0,0,0.3)",
      justifyContent: "center",
      alignItems: "center" as const,
    },
    dropdownContainer: {
      backgroundColor: colors.cardBackground,
      width: "80%" as const,
      borderRadius: 10,
      padding: 10,
    },
    dropdownItem: {
      paddingVertical: 10,
      paddingHorizontal: 20,
      borderBottomColor: colors.border,
      borderBottomWidth: 1,
    },
    dropdownText: {
      fontSize: textScale(14),
      fontWeight: "500",
      fontFamily: fontFamily.regular,
      color: colors.text,
    },
  });

  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={dropdownStyles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View style={dropdownStyles.dropdownContainer}>
          <FlatList
            data={options}
            keyExtractor={(_, index) => index.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={dropdownStyles.dropdownItem}
                onPress={() => onSelect(item)}
              >
                <Text style={dropdownStyles.dropdownText}>{item}</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const ProfileOption = ({ icon, title }: any) => {
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();
  return (
    <TouchableOpacity style={styles.optionContainer} activeOpacity={0.8}>
      <CustomIcon Icon={icon} height={20} width={20} />
      <View style={styles.optionTextContainer}>
        <Text style={[themedCommonStyles.font14, styles.optionText]}>
          {title}
        </Text>
      </View>
      <CustomIcon Icon={ICONS.ArrowRightIcon} height={8} width={8} />
    </TouchableOpacity>
  );
};

const PoliciesOption = ({ icon, title }: any) => {
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();
  return (
    <TouchableOpacity style={styles.policyOption} activeOpacity={0.8}>
      <View style={styles.policyTextContainer}>
        <CustomIcon Icon={icon} height={20} width={20} />
        <Text style={[themedCommonStyles.font14, styles.policyText]}>
          {title}
        </Text>
      </View>
      <CustomIcon Icon={ICONS.RightUpwardIcon} height={8} width={8} />
    </TouchableOpacity>
  );
};

const SettingsScreen = ({ navigation }: SettingsProps) => {
  const dispatch = useAppDispatch();

  const { userData } = useAppSelector((state) => state.user);
  const { theme, setTheme } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();
  const { settings, common } = useTranslation();
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [dropdownOptions, setDropdownOptions] = useState<string[]>([]);
  const [dropdownSetter, setDropdownSetter] = useState<
    ((value: string) => void) | null
  >(null);

  const appearanceOptions = ["Auto", "Light", "Dark"];

  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Map theme to appearance option
  const getAppearanceFromTheme = (themeValue: ThemeType): string => {
    switch (themeValue) {
      case "light":
        return "Light";
      case "dark":
        return "Dark";
      case "system":
        return "System";
      default:
        return "Light";
    }
  };

  // Map appearance option to theme
  const getThemeFromAppearance = (appearanceValue: string): ThemeType => {
    switch (appearanceValue) {
      case "Light":
        return "light";
      case "Dark":
        return "dark";
      case "System":
        return "system";
      default:
        return "light";
    }
  };

  const [appearance, setAppearanceState] = useState(
    getAppearanceFromTheme(theme)
  );

  // Custom setter for appearance that also updates the theme
  const setAppearance = (value: string) => {
    setAppearanceState(value);
    setTheme(getThemeFromAppearance(value));
  };

  const openDropdown = (options: string[], setter: (value: string) => void) => {
    setDropdownOptions(options);
    setDropdownSetter(() => setter);
    setDropdownVisible(true);
  };

  // Handle navigation to edit profile screen
  const handleEditProfile = () => {
    navigation.navigate("editProfile");
  };

  const handleSelect = (value: string) => {
    if (dropdownSetter) {
      dropdownSetter(value);
    }
    setDropdownVisible(false);
    setDropdownSetter(null);
  };

  const handleDeactivateAccount = async () => {
    Alert.alert(
      "Deactivate Account",
      "Are you sure you want to deactivate your account?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Confirm",
          onPress: async () => {
            try {
              const response = await postData(
                ENDPOINTS.deActivateAccount,
                data
              );
              console.log(response.data);
              if (response.data.success) {
                Toast.show({
                  type: "success",
                  text1: response.data.message,
                });
                deleteLocalStorageData(STORAGE_KEYS.token);
                navigation.replace("authStack", {
                  screen: "login",
                });
              }
            } catch (error: any) {
              Toast.show({
                type: "error",
                text1: error.message || "Something went wrong",
              });
            }
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <Dropdown
        visible={dropdownVisible}
        options={dropdownOptions}
        onSelect={handleSelect}
        onClose={() => setDropdownVisible(false)}
      />
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <UnifiedHeader
          title={settings("settings")}
          onBackPress={() => navigation.goBack()}
        />

        {/* Profile Section */}
        <ImageBackground
          source={ImagePath.EditBackground}
          style={styles.imageBackground}
          borderRadius={24}
        >
          <View style={styles.profileContainer}>
            <View style={styles.profileInfoContainer}>
              <View style={styles.profileImageWrapper}>
                {userData?.profilePicture ? (
                  <Image
                    source={{ uri: userData.profilePicture }}
                    style={[
                      styles.profileImage,
                      {
                        borderRadius: 100,
                      },
                    ]}
                    onLoad={() =>
                      console.log("Settings: Profile image loaded successfully")
                    }
                    onError={(error) =>
                      console.log(
                        "Settings: Profile image load error:",
                        error.nativeEvent.error
                      )
                    }
                  />
                ) : (
                  <Image
                    source={ImagePath.CalendarProfile}
                    style={styles.profileImage}
                  />
                )}
              </View>
              <View>
                <Text style={themedCommonStyles.font18White}>
                  {userData?.fullName}
                </Text>
                <Text style={themedCommonStyles.font12white}>
                  {userData?.email}
                </Text>
              </View>
            </View>
            <TouchableOpacity
              style={styles.editButton}
              activeOpacity={0.8}
              onPress={handleEditProfile}
            >
              <Text style={themedCommonStyles.font14white}>
                {common("edit")}
              </Text>
              <CustomIcon Icon={ICONS.WhiteArrowRight} height={7} width={4} />
            </TouchableOpacity>
          </View>
        </ImageBackground>

        {/* Notification & Language & Appearance */}
        <View style={styles.mainContainer}>
          {/* Notification */}
          <TouchableOpacity
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <View style={styles.policyTextContainer}>
              <CustomIcon Icon={ICONS.BellIcon} height={20} width={20} />
              <Text style={[themedCommonStyles.font14, styles.optionText]}>
                {settings("notifications")}
              </Text>
            </View>
            <CustomIcon Icon={ICONS.ArrowRightIcon} height={8} width={8} />
          </TouchableOpacity>
          {/* Language */}
          <View style={[styles.con, { marginTop: 20 }]}>
            <View style={styles.policyTextContainer}>
              <CustomIcon Icon={ICONS.LangaugeIcon} height={20} width={20} />
              <Text style={[themedCommonStyles.font14, styles.optionText]}>
                {settings("language")}
              </Text>
            </View>
            <LanguageSelector showLabel={true} style={styles.languagebtn} />
          </View>
          {/* Appearance */}
          <View style={[styles.con, { marginTop: 20 }]}>
            <View style={styles.policyTextContainer}>
              <CustomIcon Icon={ICONS.AppearanceIcon} height={20} width={20} />
              <Text style={[themedCommonStyles.font14, styles.optionText]}>
                {settings("appearance")}
              </Text>
            </View>
            <TouchableOpacity
              style={styles.languagebtn}
              activeOpacity={0.8}
              onPress={() => openDropdown(appearanceOptions, setAppearance)}
            >
              <Text
                style={[themedCommonStyles.font14Center, styles.languageText]}
              >
                {appearance}
              </Text>
              <CustomIcon Icon={ICONS.DropdownIcon} height={8} width={8} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Settings Options */}
        <ProfileOption icon={ICONS.PasswordIcon} title={settings("password")} />

        {/* Policies Section */}
        <TouchableOpacity style={styles.policiesContainer} activeOpacity={0.8}>
          <PoliciesOption
            icon={ICONS.PolicyTermsIcon}
            title={settings("privacyPolicy")}
          />
          <PoliciesOption
            icon={ICONS.PolicyTermsIcon}
            title={settings("termsOfService")}
          />
        </TouchableOpacity>

        {/* Logout & Delete Account */}
        <View style={styles.logDeleteCon}>
          <TouchableOpacity
            style={styles.logDeleteOption}
            activeOpacity={0.8}
            disabled={isLoggingOut}
            onPress={async () => {
              try {
                setIsLoggingOut(true);
                await deleteLocalStorageData(STORAGE_KEYS.token);
                await deleteLocalStorageData(STORAGE_KEYS.credentials);

                // Update auth state
                dispatch(setIsAuth(false));

                // Navigate to login screen
                navigation.replace("authStack", { screen: "LoginScreen" });
              } catch (error) {
                console.error("Error during logout:", error);
                setIsLoggingOut(false);
              }
            }}
          >
            <CustomIcon Icon={ICONS.LogoutIcon} height={20} width={20} />
            <Text style={[themedCommonStyles.font14, styles.logoutText]}>
              {settings("logout")}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.logDeleteOption, { marginTop: 20 }]}
            activeOpacity={0.8}
            onPress={handleDeactivateAccount}
          >
            <CustomIcon Icon={ICONS.DeleteAccountIcon} height={20} width={20} />
            <Text style={[themedCommonStyles.font14, styles.deleteText]}>
              {settings("deleteAccount")}
            </Text>
          </TouchableOpacity>
        </View>

        
      </ScrollView>
    </SafeAreaView>
  );
};

export default SettingsScreen;

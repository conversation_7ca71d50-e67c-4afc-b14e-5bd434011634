import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Image,
  Modal,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import {
  CallingCode,
  Country,
  CountryCode,
} from "react-native-country-picker-modal";
import DatePicker from "react-native-date-picker";
import {
  Asset,
  launchCamera,
  launchImageLibrary,
} from "react-native-image-picker";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { setNewClient, updateClient } from "../../Redux/slices/clientSlice";
import { useAppDispatch } from "../../Redux/store";
import { ThemedPhonePicker } from "../../Utilities/Components/ThemedHelpers";
import UploadImageOptions from "../../Utilities/Components/Modal/UploadImageOptions";
import { showErrorToast } from "../../Utilities/toastUtils";
import {
  moderateScale,
  moderateScaleVertical,
  textScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";
import { postData, putData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import { AddNewClientApiResponse } from "../../Services/ApiResponse";
import {
  formatDateToYYYYMMDD,
  parseDateFromYYYYMMDD,
} from "../../Utilities/Helpers";

const AddNewClient = ({ navigation, route }: any) => {
  // Check if we're editing an existing client
  const isEditing = route.params?.isEditing || false;
  const clientData = route.params?.clientData || null;
  const dispatch = useAppDispatch();
  const { colors, isDarkMode } = useTheme();
  const styles = useThemedStyles();

  // Initialize state with client data if editing
  const [businessName, setBusinessName] = useState(
    isEditing ? clientData.name : ""
  );
  const [Email, setEmail] = useState(isEditing ? clientData.email : "");
  const [phoneNumber, setPhoneNumber] = useState(
    isEditing ? extractPhoneNumber(clientData.number) : ""
  );
  const [countryVisible, setCountryVisible] = useState(false);
  const [countryCode, setCountryCode] = useState<CountryCode>("IN");
  const [callingCode, setCallingCode] = useState<CallingCode>(
    isEditing ? extractCallingCode(clientData.number) : "91"
  );

  const [isLoading, setIsLoading] = useState(false);

  // Helper function to extract phone number from formatted string
  function extractPhoneNumber(formattedNumber: string): string {
    // Extract the phone number part after the calling code
    // Example: "+91 1234567890" -> "1234567890"
    const parts = formattedNumber.split(" ");
    return parts.length > 1 ? parts[1] : "";
  }

  // Helper function to extract calling code from formatted string
  function extractCallingCode(formattedNumber: string): CallingCode {
    // Extract the calling code part
    // Example: "+91 1234567890" -> "91"
    const parts = formattedNumber.split(" ");
    if (parts.length > 0) {
      const callingCodePart = parts[0];
      return callingCodePart.replace("+", "") as CallingCode;
    }
    return "91"; // Default to 91 if extraction fails
  }

  const [isButtonEnabled, setIsButtonEnabled] = useState(false);
  const [date, setDate] = useState<any>(
    isEditing && parseDateFromYYYYMMDD(clientData.birthday)
      ? new Date(clientData.birthday)
      : null
  );
  const [open, setOpen] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedGender, setSelectedGender] = useState(
    isEditing ? clientData.gender : null
  );

  const [image, setImage] = useState<any>(isEditing ? clientData.image : null);
  const [isImageModalVisible, setIsImageModalVisible] = useState(false);

  const onClickCapture = () => {
    setIsImageModalVisible(true);
  };

  const handleImagePick = () => {
    launchImageLibrary({ mediaType: "photo", quality: 0.8 }, (response) => {
      if (response.didCancel) {
        console.log("User cancelled image picker");
      } else if (response.errorCode) {
        console.log("ImagePicker Error: ", response.errorMessage);
      } else if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        setImage(asset.uri);
      } else {
        console.log("No image selected or unexpected response:", response);
      }
      setIsImageModalVisible(false);
    });
  };

  const handleCameraPick = async () => {
    try {
      const result = await launchCamera({
        quality: 1,
        mediaType: "photo",
      });

      if (result.didCancel) {
        console.log("User cancelled camera");
      } else if (result.errorCode) {
        console.log("Camera error:", result.errorMessage);
      } else if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        setImage(asset.uri);
      } else {
        console.log("No image captured or unexpected response:", result);
      }
      setIsImageModalVisible(false);
    } catch (error) {
      console.log("Camera capture failed:", error);
      showErrorToast("Camera Error", "Failed to capture photo");
    }
  };

  const onSelect = (country: Country) => {
    setCountryCode(country.cca2);
    setCallingCode(country.callingCode?.[0] ?? ""); // Ensure calling code updates
    setCountryVisible(false); // Close the picker after selection
  };

  const openPicker = () => {
    setCountryVisible(!countryVisible);
  };

  const handleGenderSelect = (gender: any) => {
    setSelectedGender(gender);
    setModalVisible(false);
  };

  const handleSaveClient = async () => {
    setIsLoading(true);
    try {
      if (isEditing) {
        const response = await putData<AddNewClientApiResponse>(
          `${ENDPOINTS.createClient}/${clientData.id}`,
          {
            name: businessName,
            email: Email,
            phoneNumber: phoneNumber,
            countryCode: `+${callingCode}`,
            countryCallingCode: countryCode,
            profilePicture:
              "https://images.unsplash.com/photo-1472491235688-bdc81a63246e?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8",
            birthday: formatDateToYYYYMMDD(date),
            gender: selectedGender.toLowerCase(),
          }
        );

        if (response.data.success) {
          Toast.show({
            type: "success",
            text1: response.data.message,
          });

          dispatch(
            updateClient({
              id: response.data.data.client._id,
              name: response.data.data.client.name,
              email: response.data.data.client.email,
              number: `+${response.data.data.client.countryCode} ${response.data.data.client.phoneNumber}`,
              image: response.data.data.client.profilePicture,
              gender: response.data.data.client.gender,
              birthday: response.data.data.client.birthday,
              checked: false,
            })
          );
          navigation.goBack();
        }
      } else {
        const response = await postData<AddNewClientApiResponse>(
          ENDPOINTS.createClient,
          {
            name: businessName,
            email: Email,
            phoneNumber: phoneNumber,
            countryCode: `+${callingCode}`,
            countryCallingCode: countryCode,
            profilePicture:
              "https://images.unsplash.com/photo-1472491235688-bdc81a63246e?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
            birthday: formatDateToYYYYMMDD(date),
            gender: selectedGender.toLowerCase(),
          }
        );

        if (response.data.success) {
          Toast.show({
            type: "success",
            text1: response.data.message,
          });

          dispatch(
            setNewClient({
              id: response.data.data.client._id, // Simple ID generation
              name: response.data.data.client.name,
              email: response.data.data.client.email,
              number: `+${response.data.data.client.countryCode} ${response.data.data.client.phoneNumber}`,
              image: response.data.data.client.profilePicture,
              gender: response.data.data.client.gender,
              birthday: response.data.data.client.birthday,
              checked: false,
            })
          );
          navigation.goBack();
        }
      }
    } catch (error: any) {
      console.error("Error creating client:", error);
      Toast.show({
        type: "error",
        text1: error.message || "Something went wrong. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (clientData) {
      setBusinessName(clientData.name);
      setEmail(clientData.email);
      setPhoneNumber(clientData.number);
      setCallingCode(clientData.countryCode);
      setCountryCode(clientData.countryCallingCode);
      setImage(clientData.image);
      setSelectedGender(clientData.gender);
      setDate(new Date(clientData.birthday));
    }

    return () => {};
  }, [clientData]);

  useEffect(() => {
    setIsButtonEnabled(businessName.trim() !== "" && phoneNumber.trim() !== "");
  }, [businessName, Email, phoneNumber]);

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView
        bounces={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ gap: verticalScale(20) }}
      >
        {/* Header */}
        <UnifiedHeader
          title={isEditing ? "Edit client" : "Add new client"}
          onBackPress={() => navigation.goBack()}
        />

        <View style={{ paddingHorizontal: moderateScale(10) }}>
          <TouchableOpacity
            onPress={onClickCapture}
            style={styles.detailiconContainer}
          >
            {image ? (
              <Image
                source={{ uri: image }}
                style={{ height: 90, width: 90, borderRadius: 100 }}
              />
            ) : (
              <CustomIcon Icon={ICONS.DetailsStepIcon} height={30} width={30} />
            )}
            <View style={{ position: "absolute", bottom: -5, right: -5 }}>
              <CustomIcon
                Icon={image ? ICONS.EditServices : ICONS.CameraIcon}
                height={40}
                width={40}
              />
            </View>
          </TouchableOpacity>
          {/* Name */}
          <Text style={styles.labelText}>
            Name <Text style={styles.requiredText}>*</Text>
          </Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="John Doe"
              placeholderTextColor={colors.greyText}
              value={businessName}
              onChangeText={setBusinessName}
              keyboardType="default"
            />
          </View>
          {/* Email */}
          <Text style={styles.labelText}>Email</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="<EMAIL>"
              placeholderTextColor={colors.greyText}
              value={Email}
              onChangeText={setEmail}
              keyboardType="default"
            />
          </View>
          {/* Phone Number */}
          <Text style={styles.labelText}>
            Phone Number <Text style={styles.requiredText}>*</Text>
          </Text>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginTop: verticalScale(10),
            }}
          >
            <View style={styles.phoneview}>
              <ThemedPhonePicker
                visible={countryVisible}
                countryCode={countryCode}
                onSelect={onSelect}
                onPress={openPicker}
              />
            </View>
            <View style={styles.phoneCountry}>
              <TextInput
                style={[styles.input, { flex: 1 }]}
                placeholder="00 00 0000"
                placeholderTextColor={colors.greyText}
                value={phoneNumber}
                onChangeText={setPhoneNumber}
                keyboardType="number-pad"
              />
            </View>
          </View>

          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              gap: moderateScale(20),
            }}
          >
            <View style={{ marginTop: moderateScaleVertical(20), flex: 1 }}>
              <Text style={styles.labelText}>Birthday</Text>
              <TouchableOpacity
                style={styles.dategender}
                activeOpacity={0.8}
                onPress={() => setOpen(true)}
              >
                <CustomIcon Icon={ICONS.Calender} width={20} height={20} />
                <Text
                  style={[
                    styles.dateText,
                    { color: date ? colors.text : colors.greyText },
                  ]}
                >
                  {date ? formatDateToYYYYMMDD(date) : "dd / mm / yyyy"}
                </Text>
              </TouchableOpacity>

              <DatePicker
                modal
                open={open}
                maximumDate={new Date()}
                date={date ?? new Date()}
                mode="date"
                onConfirm={(selectedDate) => {
                  setOpen(false);
                  setDate(selectedDate);
                }}
                onCancel={() => setOpen(false)}
                theme={isDarkMode ? "dark" : "light"}
              />
            </View>
            <View style={{ marginTop: 20, flex: 1 }}>
              <Text style={styles.labelText}>Gender</Text>
              <TouchableOpacity
                style={styles.dategender}
                activeOpacity={0.8}
                onPress={() => setModalVisible(true)}
              >
                <Text
                  style={{
                    fontSize: textScale(14),
                    color: selectedGender ? colors.text : colors.greyText,
                  }}
                >
                  {selectedGender
                    ? selectedGender.charAt(0).toUpperCase() +
                      selectedGender.slice(1)
                    : "Select an option"}
                </Text>
              </TouchableOpacity>

              {/* Modal */}
              <Modal
                transparent={true}
                visible={modalVisible}
                animationType="slide"
              >
                <View style={styles.modalContainer}>
                  <View style={styles.modalContent}>
                    <Text style={styles.modalTitle}>Select Gender</Text>

                    <TouchableOpacity
                      style={styles.option}
                      onPress={() => handleGenderSelect("Male")}
                    >
                      <Text style={styles.optionText}>Male</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={styles.option}
                      onPress={() => handleGenderSelect("Female")}
                    >
                      <Text style={styles.optionText}>Female</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={styles.option}
                      onPress={() => handleGenderSelect("Other")}
                    >
                      <Text style={styles.optionText}>Other</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={styles.cancelButton}
                      onPress={() => setModalVisible(false)}
                    >
                      <Text style={styles.cancelText}>Cancel</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </Modal>
            </View>
          </View>

          <TouchableOpacity
            style={[
              styles.loginButton,
              {
                backgroundColor: isButtonEnabled
                  ? isDarkMode
                    ? colors.primaryBase
                    : colors.maintext
                  : colors.bgsoft,
              },
            ]}
            disabled={!isButtonEnabled}
            activeOpacity={0.8}
            onPress={handleSaveClient}
          >
            {isLoading ? (
              <ActivityIndicator />
            ) : (
              <Text
                style={[
                  styles.loginText,
                  {
                    color: isButtonEnabled ? colors.white : colors.greyText,
                  },
                ]}
              >
                {isEditing ? "Update" : "Save"}
              </Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Image Upload Options Modal */}
        {isImageModalVisible && (
          <UploadImageOptions
            isModalVisible={isImageModalVisible}
            closeModal={() => setIsImageModalVisible(false)}
            onPressCamera={handleCameraPick}
            onPressGallery={handleImagePick}
          />
        )}
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default AddNewClient;

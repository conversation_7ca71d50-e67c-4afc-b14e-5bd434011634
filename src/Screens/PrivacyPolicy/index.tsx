import { ScrollView, StyleSheet, Text, View } from "react-native";
import React, { FC } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { useThemedStyles } from "./themedStyles";
import useTranslation from "../../Localization/useTranslation";
import { PrivacyPolicyProps } from "../../Navigation/Typings";

const PrivacyPolicy: FC<PrivacyPolicyProps> = ({ navigation }) => {
  const styles = useThemedStyles();
  const { privacyPolicy, common } = useTranslation();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <UnifiedHeader
          title={privacyPolicy("privacyPolicy")}
          onBackPress={() => navigation.goBack()}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default PrivacyPolicy;

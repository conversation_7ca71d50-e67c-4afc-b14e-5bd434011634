import { Platform, StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(20),
      gap: verticalScale(20),
    },
    headerContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: 15,
    },
    headerLeftContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    headerRightContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
    },
    tabsContainer: {
      flexDirection: "row",
      marginHorizontal: moderateScale(20),
      marginBottom: verticalScale(10),
    },
    tab: {
      flex: 1,
      paddingVertical: verticalScale(10),
      alignItems: "center",
      borderBottomWidth: 2,
      borderBottomColor: colors.bgsoft,
    },
    activeTab: {
      borderBottomColor: colors.primaryBase,
    },
    tabText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.greyText,
    },
    activeTabText: {
      fontSize: 14,
      fontWeight: "700",
      color: colors.primaryBase,
    },
    inputContainer: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 10,
      paddingVertical: Platform.OS === "ios" ? 10 : 0,
      paddingHorizontal: moderateScale(10),
      marginVertical: verticalScale(10),
      marginHorizontal: moderateScale(15),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    input: {
      fontSize: 14,
      fontWeight: "400",
      flex: 1,
      marginHorizontal: moderateScale(10),
      color: colors.text,
    },
    addText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.primaryBase,
    },
    serviceContainer: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderRadius: 10,
      marginHorizontal: moderateScale(20),
      marginVertical: verticalScale(10),
    },
    categoryHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: 15,
      borderBottomWidth: 0,
      borderColor: colors.border,
    },
    categoryHeaderExpanded: {
      borderBottomWidth: 1,
    },
    categoryTitleContainer: {
      flexDirection: "row",
      gap: moderateScale(5),
      alignItems: "center",
    },
    categoryTitle: {
      fontSize: 18,
      fontWeight: "500",
      color: colors.text,
    },
    serviceItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      padding: 15,
    },
    serviceInfoContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
    },
    serviceName: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    serviceDetails: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.greyText,
    },
    moreOptionsButton: {
      width: 36,
      height: 36,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 100,
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: isDarkMode ? colors.white : colors.white,
    },
    emptyContainer: {
      alignItems: "center",
      justifyContent: "center",
      marginTop: 50,
      marginHorizontal: moderateScale(20),
    },
    noServiceText: {
      fontSize: 16,
      fontWeight: "500",
      marginTop: verticalScale(10),
      color: colors.text,
    },
    noServiceSubText: {
      fontSize: 14,
      fontWeight: "400",
      textAlign: "center",
      color: colors.greyText,
    },
    addServiceButton: {
      borderWidth: 1,
      borderRadius: 10,
      borderColor: colors.border,
      padding: moderateScale(10),
      flexDirection: "row",
      gap: moderateScale(5),
      marginTop: verticalScale(20),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    addServiceText: {
      fontSize: 14,
      color: colors.text,
    },
    // Package styles
    packageContainer: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderRadius: 10,
      marginHorizontal: moderateScale(20),
      marginVertical: verticalScale(10),
      overflow: "hidden",
    },
    packageHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: 15,
      borderBottomWidth: 0,
      borderColor: colors.border,
    },
    packageHeaderExpanded: {
      borderBottomWidth: 1,
    },
    packageTitleContainer: {
      flex: 1,
      flexDirection: "row",
      gap: moderateScale(10),
      alignItems: "center",
    },
    packageTitleInfo: {
      flex: 1,
    },
    packageTitle: {
      fontSize: 16,
      fontWeight: "500",
      color: colors.text,
    },
    packageSubtitle: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.greyText,
    },
    packageHeaderRight: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(10),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
    },
    packageServicesContainer: {
      padding: 15,
      backgroundColor: isDarkMode ? colors.bglight : colors.bglight,
    },
    packageServicesTitle: {
      fontSize: 14,
      fontWeight: "700",
      marginBottom: verticalScale(10),
      color: colors.text,
    },
    packageServiceItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      padding: 10,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderRadius: 8,
      marginBottom: verticalScale(8),
    },
    // Modal styles
    modalOverlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
    },
    modalContainer: {
      backgroundColor: colors.background,
      borderRadius: 15,
      padding: moderateScale(20),
      marginHorizontal: moderateScale(20),
      minWidth: moderateScale(280),
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: "600",
      color: colors.text,
      textAlign: "center",
      marginBottom: verticalScale(10),
    },
    modalMessage: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.greyText,
      textAlign: "center",
      marginBottom: verticalScale(20),
      lineHeight: 20,
    },
    modalButtons: {
      flexDirection: "row",
      gap: moderateScale(10),
    },
    modalButton: {
      flex: 1,
      paddingVertical: verticalScale(12),
      borderRadius: 8,
      alignItems: "center",
    },
    cancelButton: {
      backgroundColor: colors.bgsoft,
      borderWidth: 1,
      borderColor: colors.border,
    },
    deleteButton: {
      backgroundColor: colors.stateerrorbase,
    },
    cancelButtonText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    deleteButtonText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
  });
};

import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import { useDispatch, useSelector } from "react-redux";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import {
  updateBusinessDetails,
  updateContactInfo,
  updateLocationInfo,
  updateOpeningTimes,
} from "../../Redux/slices/businessSlice";
import { RootState } from "../../Redux/store";
import { GetBusinessApiResponse } from "../../Services/ApiResponse";
import { fetchData, putData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";
import Contacts from "./Steps/Contacts";
import Detail from "./Steps/Detail";
import Location from "./Steps/Location";
import OpeningTimes from "./Steps/OpeningTimes";
import { useThemedStyles } from "./themedStyles";
import useTranslation from "../../Localization/useTranslation";

const getTabsWithTranslations = (business: any) => [
  {
    id: "details",
    label: business("businessDetails"),
    selectedIcon: ICONS.DetailsIcon,
    unselectedIcon: ICONS.ProfileTabIcon,
  },
  {
    id: "location",
    label: business("location"),
    selectedIcon: ICONS.LocationSelected,
    unselectedIcon: ICONS.LocationUnselected,
  },
  {
    id: "opening",
    label: business("openingHours"),
    selectedIcon: ICONS.SelectedOpeningTime,
    unselectedIcon: ICONS.OpeningTimeIcon,
  },
  {
    id: "contacts",
    label: business("contacts"),
    selectedIcon: ICONS.SelectedContacts,
    unselectedIcon: ICONS.EmailIcon,
  },
];

const BusinessDetail = ({ navigation }: any) => {
  const dispatch = useDispatch();

  const { userData } = useSelector((state: RootState) => state.user);
  const { details, location, openingTimes, contacts } = useSelector(
    (state: RootState) => state.business
  );
  const {
    businessName,
    phoneNumber,
    callingCode,
    countryCode,
    description,
    websiteUrl,
  } = details;
  const { facebook, instagram, email } = contacts;
  const { country, city, region, streetAddress } = location;
  const [monday, tuesday, wednesday, thursday, friday, saturday, sunday] =
    openingTimes;

  const [isLoading, setIsLoading] = useState(false);
  const [isUpdateLoaindg, setIsUpdateLoaindg] = useState(false);

  const [activeIndex, setActiveIndex] = useState(0);
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const { business, common } = useTranslation();
  const tabs = getTabsWithTranslations(business);

  const handleGoBack = () => {
    navigation.goBack();
  };

  const [stepValidation, setStepValidation] = useState({
    details: false,
    location: false,
    opening: false,
    contacts: true,
  });

  const isCurrentStepValid = () => {
    const currentTabId = tabs[activeIndex]?.id;
    return stepValidation[currentTabId as keyof typeof stepValidation] || false;
  };

  const renderTabContent = () => {
    switch (tabs[activeIndex]?.id) {
      case "details":
        return <Detail key="details" />;
      case "location":
        return (
          <Location
            key="location"
            onValidationChange={(isValid) =>
              setStepValidation((prev) => ({ ...prev, location: isValid }))
            }
          />
        );
      case "opening":
        return (
          <OpeningTimes
            key="opening"
            onValidationChange={(isValid) =>
              setStepValidation((prev) => ({ ...prev, opening: isValid }))
            }
          />
        );
      case "contacts":
        return (
          <Contacts
            key="contact"
            onValidationChange={(isValid) =>
              setStepValidation((prev) => ({ ...prev, contacts: isValid }))
            }
          />
        );
      default:
        return null;
    }
  };

  const getBusinessDetails = async () => {
    setIsLoading(true);
    try {
      const response = await fetchData<GetBusinessApiResponse>(
        `${ENDPOINTS.getBusiness}/${userData?.business.businessId}`
      );
      if (response.data.success) {
        const businessData = response.data.data;

        dispatch(
          updateBusinessDetails({
            businessEmail: businessData.businessProfile.email,
            businessName: businessData.businessProfile.businessName,
            description: businessData.businessProfile.businessDescription,
            phoneNumber: businessData.businessProfile.PhoneNumber,
            callingCode: businessData.businessProfile.countryCode,
            countryCode: businessData.businessProfile.countryCallingCode,
            websiteUrl: businessData.businessProfile.websiteLink,
          })
        );

        dispatch(
          updateLocationInfo({
            country: businessData.businessProfile.country,
            city: businessData.businessProfile.address.city,
            region: businessData.businessProfile.address.region,
            streetAddress: businessData.businessProfile.address.street,
          })
        );

        dispatch(
          updateContactInfo({
            email: businessData.businessProfile.email,
            facebook: businessData.businessProfile.facebookLink,
            instagram: businessData.businessProfile.instagramLink,
          })
        );

        dispatch(
          updateOpeningTimes([
            {
              day: "Monday",
              isChecked:
                businessData.businessProfile.businessHours.monday.isOpen,
              isOpen: businessData.businessProfile.businessHours.monday.isOpen,
              timeSlots:
                businessData.businessProfile.businessHours.monday.timeSlots.map(
                  (slot) => ({
                    startTime: slot.open,
                    endTime: slot.close,
                  })
                ),
            },
            {
              day: "Tuesday",
              isChecked:
                businessData.businessProfile.businessHours.tuesday.isOpen,
              isOpen: businessData.businessProfile.businessHours.tuesday.isOpen,
              timeSlots:
                businessData.businessProfile.businessHours.tuesday.timeSlots.map(
                  (slot) => ({
                    startTime: slot.open,
                    endTime: slot.close,
                  })
                ),
            },
            {
              day: "Wednesday",
              isChecked:
                businessData.businessProfile.businessHours.wednesday.isOpen,
              isOpen:
                businessData.businessProfile.businessHours.wednesday.isOpen,
              timeSlots:
                businessData.businessProfile.businessHours.wednesday.timeSlots.map(
                  (slot) => ({
                    startTime: slot.open,
                    endTime: slot.close,
                  })
                ),
            },
            {
              day: "Thursday",
              isChecked:
                businessData.businessProfile.businessHours.thursday.isOpen,
              isOpen:
                businessData.businessProfile.businessHours.thursday.isOpen,
              timeSlots:
                businessData.businessProfile.businessHours.thursday.timeSlots.map(
                  (slot) => ({
                    startTime: slot.open,
                    endTime: slot.close,
                  })
                ),
            },
            {
              day: "Friday",
              isChecked:
                businessData.businessProfile.businessHours.friday.isOpen,
              isOpen: businessData.businessProfile.businessHours.friday.isOpen,
              timeSlots:
                businessData.businessProfile.businessHours.friday.timeSlots.map(
                  (slot) => ({
                    startTime: slot.open,
                    endTime: slot.close,
                  })
                ),
            },
            {
              day: "Saturday",
              isChecked:
                businessData.businessProfile.businessHours.saturday.isOpen,
              isOpen:
                businessData.businessProfile.businessHours.saturday.isOpen,
              timeSlots:
                businessData.businessProfile.businessHours.saturday.timeSlots.map(
                  (slot) => ({
                    startTime: slot.open,
                    endTime: slot.close,
                  })
                ),
            },
            {
              day: "Sunday",
              isChecked:
                businessData.businessProfile.businessHours.sunday.isOpen,
              isOpen: businessData.businessProfile.businessHours.sunday.isOpen,
              timeSlots:
                businessData.businessProfile.businessHours.sunday.timeSlots.map(
                  (slot) => ({
                    startTime: slot.open,
                    endTime: slot.close,
                  })
                ),
            },
          ])
        );
      }
    } catch (error: any) {
      console.error("Error fetching business details:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveBusinessDetails = async () => {
    setIsUpdateLoaindg(true);

    try {
      const response = await putData<any>(
        `${ENDPOINTS.updateBusiness}/${userData?.business.businessId}`,
        {
          businessName,
          email,
          businessDescription: description,
          phoneNumber,
          countryCode: `+${callingCode}`,
          countryCallingCode: countryCode,
          websiteLink: websiteUrl,
          facebookLink: facebook,
          instagramLink: instagram,
          country,
          address: {
            street: streetAddress,
            city,
            region,
            country,
          },
          businessHours: {
            monday: {
              timeSlots: monday.timeSlots.map((slot) => ({
                open: slot.startTime,
                close: slot.endTime,
              })),
              isOpen: monday.isOpen,
            },
            tuesday: {
              timeSlots: tuesday.timeSlots.map((slot) => ({
                open: slot.startTime,
                close: slot.endTime,
              })),
              isOpen: tuesday.isOpen,
            },
            wednesday: {
              timeSlots: wednesday.timeSlots.map((slot) => ({
                open: slot.startTime,
                close: slot.endTime,
              })),
              isOpen: wednesday.isOpen,
            },
            thursday: {
              timeSlots: thursday.timeSlots.map((slot) => ({
                open: slot.startTime,
                close: slot.endTime,
              })),
              isOpen: thursday.isOpen,
            },
            friday: {
              timeSlots: friday.timeSlots.map((slot) => ({
                open: slot.startTime,
                close: slot.endTime,
              })),
              isOpen: friday.isOpen,
            },
            saturday: {
              timeSlots: saturday.timeSlots.map((slot) => ({
                open: slot.startTime,
                close: slot.endTime,
              })),
              isOpen: saturday.isOpen,
            },
            sunday: {
              timeSlots: sunday.timeSlots.map((slot) => ({
                open: slot.startTime,
                close: slot.endTime,
              })),
              isOpen: sunday.isOpen,
            },
          },
        }
      );

      if (response.data.success) {
        if (activeIndex < tabs.length - 1) {
          setActiveIndex(activeIndex + 1);
        } else {
          navigation.goBack();
        }
        Toast.show({
          type: "success",
          text1: response.data.message,
        });
      }
    } catch (error: any) {
      console.error("Error updating business details:", error);
      Toast.show({
        type: "error",
        text1: common("somethingWentWrong"),
      });
    } finally {
      setIsUpdateLoaindg(false);
    }
  };

  useEffect(() => {
    const isBusinessNameValid = businessName.trim().length > 0;
    const isPhoneValid = /^\d+$/.test(phoneNumber);
    setStepValidation((prev) => ({
      ...prev,
      details: isBusinessNameValid && isPhoneValid,
    }));
  }, [businessName, phoneNumber]);

  useEffect(() => {
    if (userData?.business) {
      getBusinessDetails();
    }
  }, []);

  if (isLoading) {
    return (
      <SafeAreaView
        style={[styles.container, { flex: 1, justifyContent: "center" }]}
      >
        <ActivityIndicator size="large" color={colors.primaryBase} />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <UnifiedHeader
        title={business("setupBusiness")}
        onBackPress={handleGoBack}
      />

      {/* Scrollable Horizontal Tab Navigation */}
      <View
        style={{
          paddingVertical: verticalScale(20),
          paddingHorizontal: moderateScale(15),
        }}
      >
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View
            style={[
              styles.tabContainer,
              {
                borderBottomColor: colors.bgsoft,
              },
            ]}
          >
            {tabs.map((item, index) => {
              const isSelected = activeIndex === index;
              return (
                <TouchableOpacity
                  key={item.id}
                  activeOpacity={1}
                  onPress={() => setActiveIndex(index)}
                  style={[
                    styles.tab,
                    {
                      borderColor: isSelected
                        ? colors.primaryBase
                        : colors.bgsoft,
                      borderBottomWidth: isSelected ? 1.4 : 1,
                    },
                  ]}
                >
                  <View style={styles.tabRow}>
                    <CustomIcon
                      Icon={
                        isSelected ? item.selectedIcon : item.unselectedIcon
                      }
                      height={20}
                      width={20}
                    />
                    <Text
                      style={[
                        styles.tabText,
                        isSelected && styles.selectedTabText,
                      ]}
                    >
                      {item.label}
                    </Text>
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        </ScrollView>
      </View>

      {/* Render Tab Content */}
      <View
        style={{
          paddingHorizontal: moderateScale(10),
          width: "100%",
          flex: 1,
        }}
      >
        {renderTabContent()}
      </View>

      <TouchableOpacity
        style={[
          styles.nextbtn,
          {
            opacity: isCurrentStepValid() ? 1 : 0.5,
          },
        ]}
        disabled={!isCurrentStepValid()}
        onPress={handleSaveBusinessDetails}
      >
        {isUpdateLoaindg ? (
          <ActivityIndicator size="small" color={colors.white} />
        ) : (
          <>
            <Text style={styles.nextBtnText}>
              {activeIndex === tabs.length - 1
                ? business("saveBusiness")
                : common("next")}
            </Text>
            {activeIndex !== tabs.length - 1 && (
              <CustomIcon Icon={ICONS.WhiteArrow} height={12} width={12} />
            )}
          </>
        )}
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default BusinessDetail;

import React, { useEffect, useState } from "react";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import DatePicker from "react-native-date-picker";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import useTranslation from "../../../Localization/useTranslation";
import { updateOpeningTimes } from "../../../Redux/slices/businessSlice";
import { useAppDispatch, useAppSelector } from "../../../Redux/store";
import { moderateScale } from "../../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedOpeningTimesStyles } from "./themedOpeningTimesStyles";

// Helper function to format time
const formatDateToTime = (date: Date): string => {
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  return `${hours}:${minutes}`;
};

// Helper function to parse time string to Date
const parseTimeToDate = (timeString: string): Date => {
  const [hours, minutes] = timeString.split(":").map(Number);
  const date = new Date();
  date.setHours(hours, minutes, 0, 0);
  return date;
};

interface TimePickerProps {
  time: string;
  onTimeChange: (time: string) => void;
}

interface OpeningTimesProps {
  onValidationChange?: (isValid: boolean) => void;
}

const TimeSelector: React.FC<TimePickerProps> = ({ time, onTimeChange }) => {
  const [showPicker, setShowPicker] = useState(false);
  const { isDarkMode } = useTheme();
  const styles = useThemedOpeningTimesStyles();

  return (
    <View style={{ flex: 1 }}>
      <TouchableOpacity
        style={styles.timeBox}
        onPress={() => setShowPicker(true)}
      >
        <Text style={styles.timeText}>{time}</Text>
        <CustomIcon Icon={ICONS.DropdownIcon} width={8} height={8} />
      </TouchableOpacity>

      <DatePicker
        modal
        open={showPicker}
        date={parseTimeToDate(time)}
        mode="time"
        theme={isDarkMode ? "dark" : "light"}
        onConfirm={(selectedTime) => {
          setShowPicker(false);
          const formattedTime = formatDateToTime(selectedTime);
          onTimeChange(formattedTime);
        }}
        onCancel={() => setShowPicker(false)}
      />
    </View>
  );
};

const OpeningTimes: React.FC<OpeningTimesProps> = ({ onValidationChange }) => {
  const dispatch = useAppDispatch();
  const savedOpeningTimes = useAppSelector(
    (state) => state.business.openingTimes
  );
  const { business, common } = useTranslation();

  const { colors } = useTheme();
  const styles = useThemedOpeningTimesStyles();

  const [copiedDay, setCopiedDay] = useState<{
    isChecked: boolean;
    isOpen: boolean;
    timeSlots: { startTime: string; endTime: string }[];
  } | null>(null);
  // Validate that at least one day is checked and open
  // and save to Redux
  useEffect(() => {
    const valid = savedOpeningTimes.some((day) => day.isChecked && day.isOpen);

    if (onValidationChange) {
      onValidationChange(valid);
    }
  }, [savedOpeningTimes]);

  // Toggle day checked state
  const toggleCheck = (index: number) => {
    // Create a deep copy of the entire state
    const newDays = savedOpeningTimes.map((day, i) => {
      if (i !== index) return { ...day, timeSlots: [...day.timeSlots] };

      // For the day we're updating, toggle the isChecked property
      return {
        ...day,
        isChecked: !day.isChecked,
        timeSlots: [...day.timeSlots],
      };
    });

    dispatch(updateOpeningTimes(newDays));
  };

  // Toggle day open state
  const toggleOpen = (index: number) => {
    // Create a deep copy of the entire state
    const newDays = savedOpeningTimes.map((day, i) => {
      if (i !== index) return { ...day, timeSlots: [...day.timeSlots] };

      // For the day we're updating, toggle the isOpen property
      const isOpen = !day.isOpen;
      return {
        ...day,
        isOpen,
        // If closing the day, uncheck it
        isChecked: isOpen ? day.isChecked : false,
        timeSlots: [...day.timeSlots],
      };
    });

    dispatch(updateOpeningTimes(newDays));
  };

  // Add a new time slot to a day
  const addTimeSlot = (index: number) => {
    // Create a deep copy of the entire state
    const newDays = savedOpeningTimes.map((day, i) => {
      if (i !== index) return { ...day, timeSlots: [...day.timeSlots] };

      // For the day we're updating, create a deep copy of its time slots and add a new one
      return {
        ...day,
        timeSlots: [...day.timeSlots, { startTime: "09:00", endTime: "17:00" }],
      };
    });

    dispatch(updateOpeningTimes(newDays));
  };

  // Remove a time slot from a day
  const removeTimeSlot = (dayIndex: number, slotIndex: number) => {
    if (savedOpeningTimes[dayIndex].timeSlots.length <= 1) {
      return; // Don't remove if it's the only time slot
    }

    // Create a deep copy of the entire state
    const newDays = savedOpeningTimes.map((day, i) => {
      if (i !== dayIndex) return { ...day, timeSlots: [...day.timeSlots] };

      // For the day we're updating, create a deep copy of its time slots without the one to remove
      return {
        ...day,
        timeSlots: day.timeSlots.filter((_, j) => j !== slotIndex),
      };
    });

    dispatch(updateOpeningTimes(newDays));
  };

  // Update a time slot
  const updateTimeSlot = (
    dayIndex: number,
    slotIndex: number,
    field: "startTime" | "endTime",
    value: string
  ) => {
    console.log(
      `Updating time slot: day=${dayIndex}, slot=${slotIndex}, field=${field}, value=${value}`
    );

    // Create a deep copy of the entire state to avoid modifying read-only properties
    const newDays = savedOpeningTimes.map((day, i) => {
      if (i !== dayIndex) return { ...day, timeSlots: [...day.timeSlots] };

      // For the day we're updating, create a deep copy of its time slots
      const newTimeSlots = day.timeSlots.map((slot, j) => {
        if (j !== slotIndex) return { ...slot };
        // For the slot we're updating, create a new object with the updated field
        return { ...slot, [field]: value };
      });

      return { ...day, timeSlots: newTimeSlots };
    });

    dispatch(updateOpeningTimes(newDays));
  };

  // Copy day settings
  const copyDaySettings = (index: number) => {
    const { isChecked, isOpen, timeSlots } = savedOpeningTimes[index];
    setCopiedDay({
      isChecked,
      isOpen,
      timeSlots: JSON.parse(JSON.stringify(timeSlots)), // Deep copy
    });
  };

  // Paste day settings
  const pasteDaySettings = (index: number) => {
    if (!copiedDay) return;

    // Create a deep copy of the entire state
    const newDays = savedOpeningTimes.map((day, i) => {
      if (i !== index) return { ...day, timeSlots: [...day.timeSlots] };

      // For the day we're updating, apply the copied settings
      return {
        ...day,
        isChecked: copiedDay.isChecked,
        isOpen: copiedDay.isOpen,
        // Create a deep copy of the copied time slots
        timeSlots: JSON.parse(JSON.stringify(copiedDay.timeSlots)),
      };
    });

    dispatch(updateOpeningTimes(newDays));
  };

  return (
    <View style={{ flex: 1 }}>
      <View style={styles.inputContainer}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ marginVertical: moderateScale(10) }}
        >
          {savedOpeningTimes?.map((item, index) => (
            <View key={item.day} style={styles.itemContainer}>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                  }}
                >
                  {item.isOpen && (
                    <TouchableOpacity
                      style={styles.checkboxContainer}
                      onPress={() => toggleCheck(index)}
                      activeOpacity={0.8}
                    >
                      {item.isChecked ? (
                        <View style={[styles.checkbox, styles.checkedBox]}>
                          <CustomIcon
                            Icon={ICONS.CheckRightIcon}
                            height={12}
                            width={12}
                          />
                        </View>
                      ) : (
                        <View style={styles.checkbox} />
                      )}
                    </TouchableOpacity>
                  )}
                  <Text
                    style={[
                      styles.dayText,
                      !item.isOpen && styles.dayTextClosed,
                      item.isChecked && styles.dayTextChecked,
                    ]}
                  >
                    {item.day}
                  </Text>
                </View>
                <View style={styles.actionButtons}>
                  {item.isOpen && (
                    <>
                      <TouchableOpacity
                        onPress={() => copyDaySettings(index)}
                        style={styles.copyPasteButton}
                        activeOpacity={0.8}
                      >
                        <Text style={styles.copyPasteText}>
                          {common("copy")}
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => pasteDaySettings(index)}
                        style={[
                          styles.copyPasteButton,
                          !copiedDay && styles.disabledButton,
                        ]}
                        activeOpacity={0.8}
                        disabled={!copiedDay}
                      >
                        <Text
                          style={[
                            styles.copyPasteText,
                            !copiedDay && styles.disabledText,
                          ]}
                        >
                          {common("paste")}
                        </Text>
                      </TouchableOpacity>
                    </>
                  )}
                  <TouchableOpacity
                    style={styles.toggleClosedButton}
                    onPress={() => toggleOpen(index)}
                    activeOpacity={0.8}
                  >
                    <Text
                      style={{
                        ...styles.copyPasteText,
                        color: item.isOpen
                          ? colors.stateerrorbase
                          : colors.primaryBase,
                      }}
                    >
                      {item.isOpen ? common("close") : common("open")}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Time slots */}
              {item.isOpen &&
                item.isChecked &&
                item.timeSlots.map((slot, slotIndex) => (
                  <View key={`slot-${slotIndex}`} style={styles.timeContainer}>
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        flex: 1,
                        gap: moderateScale(5),
                      }}
                    >
                      <TimeSelector
                        time={slot.startTime}
                        onTimeChange={(newTime: string) =>
                          updateTimeSlot(index, slotIndex, "startTime", newTime)
                        }
                      />
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: "400",
                          textAlign: "center",
                          color: colors.text,
                        }}
                      >
                        {" "}
                        -{" "}
                      </Text>
                      <TimeSelector
                        time={slot.endTime}
                        onTimeChange={(newTime: string) =>
                          updateTimeSlot(index, slotIndex, "endTime", newTime)
                        }
                      />
                    </View>
                    {item.timeSlots.length > 1 && (
                      <TouchableOpacity
                        onPress={() => removeTimeSlot(index, slotIndex)}
                        disabled={item.timeSlots.length === 1}
                      >
                        <CustomIcon
                          Icon={ICONS.RedCrossIcon}
                          width={12}
                          height={12}
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                ))}

              {/* Add time slot button */}
              {item.isOpen && item.isChecked && (
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={() => addTimeSlot(index)}
                  activeOpacity={0.8}
                >
                  <Text
                    style={{
                      ...styles.copyPasteText,
                      textAlign: "center",
                      marginTop: 10,
                    }}
                  >
                    + {business("setupSteps.addTimeSlot")}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          ))}
        </ScrollView>
      </View>
    </View>
  );
};

export default OpeningTimes;

import React, { useState } from "react";
import { Text, TextInput, View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { updateBusinessDetails } from "../../../Redux/slices/businessSlice";
import { useAppDispatch, useAppSelector } from "../../../Redux/store";
import { ThemedPhonePicker } from "../../../Utilities/Components/ThemedHelpers";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedDetailStyles } from "./themedDetailStyles";

const Detail = ({}: any) => {
  const { colors } = useTheme();
  const styles = useThemedDetailStyles();
  const dispatch = useAppDispatch();
  const { details } = useAppSelector((state) => state.business);
  const {
    businessName,
    description,
    phoneNumber,
    countryCode,
    callingCode,
    websiteUrl,
  } = details;

  const [countryVisible, setCountryVisible] = useState(false);

  const handleCountrySelect = (country: any) => {
    dispatch(
      updateBusinessDetails({
        countryCode: country.cca2,
        callingCode: country.callingCode ? country.callingCode[0] : "1",
      })
    );
  };

  const toggleCountryPicker = () => {
    setCountryVisible((prev: any) => !prev);
  };

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>
            Business Name <Text style={styles.required}>*</Text>
          </Text>
          <TextInput
            style={styles.input}
            placeholder="The King's Cut"
            placeholderTextColor={colors.greyText}
            value={businessName}
            onChangeText={(text) =>
              dispatch(updateBusinessDetails({ businessName: text }))
            }
            keyboardType="default"
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="Give a brief description about your business and the services you provide..."
            placeholderTextColor={colors.greyText}
            multiline
            value={description}
            onChangeText={(text) =>
              dispatch(updateBusinessDetails({ description: text }))
            }
          />
          <View style={styles.resizetext}>
            <Text style={styles.charCount}>{description.length}/200</Text>
            <CustomIcon Icon={ICONS.ResizeText} height={12} width={12} />
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>
            Phone Number <Text style={styles.required}>*</Text>
          </Text>
          <View style={styles.phoneRow}>
            <View style={styles.phoneContainer}>
              <ThemedPhonePicker
                visible={countryVisible}
                countryCode={(countryCode as any) || "IN"}
                onSelect={handleCountrySelect}
                onPress={toggleCountryPicker}
              />
            </View>
            <View style={styles.phoneInputContainer}>
              <TextInput
                style={styles.input}
                placeholder="00 00 0000"
                placeholderTextColor={colors.greyText}
                value={phoneNumber}
                onChangeText={(text) =>
                  dispatch(updateBusinessDetails({ phoneNumber: text }))
                }
                keyboardType="number-pad"
              />
            </View>
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Website</Text>
          <View style={styles.websiteRow}>
            <CustomIcon Icon={ICONS.WebsiteUrlIcon} width={16} height={16} />
            <TextInput
              style={styles.flexInput}
              placeholder="Enter your website URL"
              placeholderTextColor={colors.greyText}
              value={websiteUrl}
              onChangeText={(text) =>
                dispatch(updateBusinessDetails({ websiteUrl: text }))
              }
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
};

export default Detail;

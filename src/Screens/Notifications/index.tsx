import { ScrollView, StyleSheet, Text, View } from "react-native";
import React, { FC } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { useThemedStyles } from "../PrivacyPolicy/themedStyles";
import { NotificationsProps } from "../../Navigation/Typings";
import useTranslation from "../../Localization/useTranslation";

const Notifications: FC<NotificationsProps> = ({ navigation }) => {
  const styles = useThemedStyles();
  const { notifications, common } = useTranslation();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <UnifiedHeader
          title={notifications("notifications")}
          onBackPress={() => navigation.goBack()}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default Notifications;

const styles = StyleSheet.create({});

import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Image,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { Country, CountryCode } from "react-native-country-picker-modal";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import ThemedStatusBar from "../../Components/ThemedStatusBar";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { useAppDispatch, useAppSelector } from "../../Redux/store";
import { putData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import ThemedUploadImageOptions from "../../Utilities/Components/Modal/ThemedUploadImageOptions";
import { ThemedPhonePicker } from "../../Utilities/Components/ThemedHelpers";
import { Colors } from "../../Utilities/Styles/colors";
import { verticalScale } from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";
import { showErrorToast } from "../../Utilities/toastUtils";
import { useThemedStyles } from "./themedStyles";
import { GetProfileApiResponse } from "../../Services/ApiResponse";
import { setUser } from "../../Redux/slices/userSlice";

const EditProfile = ({ navigation }: any) => {
  const dispatch = useAppDispatch();
  const { userData } = useAppSelector((state) => state.user);

  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [countryCode, setCountryCode] = useState<CountryCode>("IN");
  const [callingCode, setCallingCode] = useState("91");
  const [countryVisible, setCountryVisible] = useState(false);
  const [profileImage, setProfileImage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showImageOptions, setShowImageOptions] = useState(false);

  const { colors } = useTheme();
  const styles = useThemedStyles();

  useEffect(() => {
    if (userData?.fullName) {
      setName(userData?.fullName);
    }

    if (userData?.email) {
      setEmail(userData?.email);
    }

    if (userData?.phoneNumber) {
      setPhoneNumber(userData?.phoneNumber);
    }

    if (userData?.countryCode) {
      setCountryCode(userData?.countryCallingCode as CountryCode);
    }

    if (userData?.countryCode) {
      setCallingCode(userData?.countryCode);
    }

    // Set profile image from user data
    if (userData?.profilePicture) {
      setProfileImage(userData?.profilePicture);
    }
  }, [userData]);

  const handleSave = async () => {
    setIsSaving(true);
    if (!userData) {
      showErrorToast("Error", "No user data available");
      return;
    }
    try {
      const response = await putData<GetProfileApiResponse>(
        ENDPOINTS.getUserProfileData,
        {
          fullName: name,
          email: email,
          phoneNumber: phoneNumber,
          countryCode: `+${callingCode}`,
          countryCallingCode: countryCode,
          profileImage: profileImage,
        }
      );

      if (response.data.success) {
        Toast.show({
          type: "success",
          text1: response.data.message,
        });
      }

      // Update local state
      dispatch(setUser(response.data.data.profile));

      setTimeout(() => {
        navigation.goBack();
      }, 300);
    } catch (error: any) {
      console.error("Error updating profile:", error);
      Toast.show({
        type: "error",
        text1: error.message || "Something went wrong. Please try again.",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleImagePick = () => {
    launchImageLibrary({ mediaType: "photo", quality: 0.8 }, (response) => {
      if (response.didCancel) {
        console.log("User cancelled image picker");
      } else if (response.errorCode) {
        console.log("ImagePicker Error: ", response.errorMessage);
      } else if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        const imageData: any = {
          uri: asset.uri || "",
          width: asset.width,
          height: asset.height,
          type: asset.type,
        };
        console.log("Selected image:", imageData);
        setProfileImage(imageData.uri);
        console.log("Profile image state updated to:", imageData.uri);
      } else {
        console.log("No image selected or unexpected response:", response);
      }
      setShowImageOptions(false);
    });
  };

  const handleCameraPick = async () => {
    try {
      const result = await launchCamera({
        quality: 1,
        mediaType: "photo",
      });

      if (result.didCancel) {
        console.log("User cancelled camera");
      } else if (result.errorCode) {
        console.log("Camera error:", result.errorMessage);
      } else if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const imageData: any = {
          uri: asset.uri || "",
          width: asset.width,
          height: asset.height,
          type: asset.type,
        };
        console.log("Captured image:", imageData);
        setProfileImage(imageData.uri);
        console.log("Profile image state updated to:", imageData.uri);
      } else {
        console.log("No image captured or unexpected response:", result);
      }
      setShowImageOptions(false);
    } catch (error) {
      console.log("Camera capture failed:", error);
      showErrorToast("Camera Error", "Failed to capture photo");
    }
  };

  const onSelect = (country: Country) => {
    setCountryCode(country.cca2);
    setCallingCode(country.callingCode[0]);
  };

  const openPicker = () => {
    setCountryVisible(!countryVisible);
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <ThemedStatusBar />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primaryBase} />
          <Text style={styles.loadingText}>Loading profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ThemedStatusBar />
      {/* Unified Header */}
      <UnifiedHeader
        title="Edit Profile"
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Image */}
        <View style={styles.imageContainer}>
          <TouchableOpacity
            onPress={() => setShowImageOptions(true)}
            style={styles.imageWrapper}
          >
            {profileImage ? (
              <>
                <Image
                  source={{ uri: profileImage }}
                  style={styles.profileImage}
                  onLoad={() => console.log("Image loaded successfully")}
                  onError={(error) =>
                    console.log("Image load error:", error.nativeEvent.error)
                  }
                />
              </>
            ) : (
              <View style={styles.placeholderImage}>
                <Text style={styles.placeholderText}>
                  {name.charAt(0).toUpperCase()}
                </Text>
              </View>
            )}
            <View style={styles.editIconContainer}>
              <CustomIcon Icon={ICONS.EditServices} height={20} width={20} />
            </View>
          </TouchableOpacity>
          <Text style={styles.changePhotoText}>Change Profile Photo</Text>
        </View>

        {/* Form Fields */}
        <View style={styles.formContainer}>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Full Name</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder="Enter your full name"
              placeholderTextColor={Colors.greyText}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Email</Text>
            <TextInput
              style={[styles.input, styles.disabledInput]}
              value={userData?.email || ""}
              editable={false}
              placeholder="Email"
              placeholderTextColor={Colors.greyText}
            />
            <Text style={styles.helperText}>Email cannot be changed</Text>
          </View>

          {/* Phone Number Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Phone Number</Text>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginTop: verticalScale(10),
              }}
            >
              <View style={styles.phoneContainer}>
                <ThemedPhonePicker
                  visible={countryVisible}
                  countryCode={countryCode}
                  onSelect={onSelect}
                  onPress={openPicker}
                />
              </View>
              <View style={styles.phoneInput}>
                <TextInput
                  style={styles.input}
                  placeholder="00 00 0000"
                  placeholderTextColor={Colors.greyText}
                  value={phoneNumber}
                  onChangeText={setPhoneNumber}
                  keyboardType="phone-pad"
                />
              </View>
            </View>
          </View>
        </View>

        {/* Image Selection Options */}
        <ThemedUploadImageOptions
          isModalVisible={showImageOptions}
          closeModal={() => setShowImageOptions(false)}
          onPressCamera={handleCameraPick}
          onPressGallery={handleImagePick}
        />
      </ScrollView>

      {/* Save Button */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSave}
          disabled={isSaving}
        >
          {isSaving ? (
            <ActivityIndicator color={Colors.white} size="small" />
          ) : (
            <Text style={styles.saveButtonText}>Save Changes</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default EditProfile;

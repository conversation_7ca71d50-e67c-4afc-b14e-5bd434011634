import React, { useEffect } from "react";
import { Image, ScrollView, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import useNetworkStatus from "../../Hooks/useNetworkStatus";
import { ManageProps } from "../../Navigation/Typings";
import { setCategories } from "../../Redux/slices/categorySlice";
import { setClients } from "../../Redux/slices/clientSlice";
import { setMembers } from "../../Redux/slices/memberSlice";
import { useAppDispatch, useAppSelector } from "../../Redux/store";
import {
  GetAllClientsApiResponse,
  GetAllTeamMembersApiResponse,
  GetAllUserCategoriesApiResponse,
} from "../../Services/ApiResponse";
import { fetchData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import ImagePath from "../../Utilities/Constants/ImagePath";
import { useThemedStyles } from "./themedStyles";

const ProfileOption = ({
  icon,
  title,
  subtitle,
  onPress,
  navigation,
  screenName,
  styles,
}: any) => (
  <TouchableOpacity
    style={styles.optionContainer}
    activeOpacity={onPress ? 0.8 : 1}
    onPress={() => {
      if (screenName) {
        navigation.navigate(screenName);
      }
    }}
  >
    <CustomIcon Icon={icon} height={40} width={40} />
    <View style={styles.optionTextContainer}>
      <Text style={styles.optionTitle}>{title}</Text>
      <Text style={styles.optionSubtitle}>{subtitle}</Text>
    </View>
    <CustomIcon Icon={ICONS.ArrowRightIcon} height={12} width={12} />
  </TouchableOpacity>
);

const ManageScreen = ({ navigation }: ManageProps) => {
  const { userData } = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();

  const { isConnected, isInternetReachable, type } = useNetworkStatus();
  const styles = useThemedStyles();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Manage</Text>
          {isConnected && (
            <TouchableOpacity
              onPress={() => {
                navigation.navigate("settings");
              }}
            >
              <Image
                source={{ uri: userData?.profilePicture }}
                style={styles.profileImage}
              />
            </TouchableOpacity>
          )}
        </View>

        {/* Content */}
        {!isConnected ? (
          <View style={styles.noConnectionContainer}>
            <Text style={styles.noConnectionText}>No Internet Connection</Text>
            <Text style={styles.noConnectionSubText}>
              Please check your internet connection and try again.
            </Text>
            <TouchableOpacity style={styles.tryAgainButton}>
              <CustomIcon Icon={ICONS.SearchIcon} height={15} width={15} />
              <Text style={styles.tryAgainText}>Try again</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            <ProfileOption
              icon={ICONS.setupbusiness}
              title="Setup your business"
              subtitle="Setup a new account for your business"
              navigation={navigation}
              screenName="businessDetails"
              styles={styles}
            />

            {/* Services & Categories Section */}
            <View style={styles.row}>
              {[
                {
                  icon: ICONS.ServicesListicon,
                  title: "List of services",
                  subtitle:
                    "Manage and organize the services your business offers.",
                  screenName: "serviceList",
                },
                {
                  icon: ICONS.ServicesListicon,
                  title: "Categories",
                  subtitle:
                    "Group your services into categories for better organization.",
                  screenName: "CategoriesScreen",
                },
              ].map((item, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.card}
                  activeOpacity={0.8}
                  onPress={() => {
                    item.screenName === "serviceList"
                      ? navigation.navigate("serviceList")
                      : navigation.navigate("manageCategories");
                  }}
                >
                  <CustomIcon Icon={item.icon} height={40} width={40} />
                  <View style={styles.cardTextContainer}>
                    <Text style={styles.cardTitle}>{item.title}</Text>
                    <Text style={styles.cardSubtitle}>{item.subtitle}</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>

            {/* Team Section */}
            <View style={styles.teamContainer}>
              <Image
                source={ImagePath.ScheduleImage}
                resizeMode="contain"
                style={styles.image}
              />
              <View style={styles.teamTextContainer}>
                <Text style={styles.teamTitle}>Team</Text>
                <Text style={styles.teamSubtitle}>
                  Manage team members and their schedules
                </Text>
                <TouchableOpacity
                  style={styles.addmemberbtn}
                  activeOpacity={0.8}
                  onPress={() => navigation.navigate("manageTeam")}
                >
                  <Text style={styles.teamButtonText}>Manage team</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Clients Section */}
            <ProfileOption
              icon={ICONS.ClientsLooIcon}
              title="Clients"
              subtitle="Add and manage your client database."
              navigation={navigation}
              screenName="manageClient"
              styles={styles}
            />
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default ManageScreen;

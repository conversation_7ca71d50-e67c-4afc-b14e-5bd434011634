import React, { useEffect, useState } from "react";
import { Text, TextInput, TouchableOpacity, View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { addCategory, updateCategory } from "../../Redux/slices/categorySlice"; // Import the action
import { useAppDispatch } from "../../Redux/store";
import { AddNewCategoryAPiResponse } from "../../Services/ApiResponse";
import { postData, putData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import { moderateScale } from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";

interface NewCategoryProps {
  navigation: any;
  route: {
    params?: {
      categoryToEdit: {
        id: string;
        name: string;
        description: string;
      } | null;
    };
  };
}

const NewCategory = ({ navigation, route }: NewCategoryProps) => {
  const dispatch = useAppDispatch();
  const { colors } = useTheme();
  const styles = useThemedStyles();

  const categoryToEdit = route.params?.categoryToEdit || null;
  const isEditing = !!categoryToEdit;

  const [description, setDescription] = useState(
    categoryToEdit?.description || ""
  );
  const [categoryName, setCategoryName] = useState(categoryToEdit?.name || "");
  const [charCount, setCharCount] = useState(
    categoryToEdit?.description?.length || 0
  );

  useEffect(() => {
    setCharCount(description.length);
  }, [description]);

  const handleSaveCategory = async () => {
    if (categoryToEdit) {
      try {
        const response = await putData<AddNewCategoryAPiResponse>(
          `${ENDPOINTS.createCategory}/${categoryToEdit.id}`,
          {
            name: categoryName,
            description: description,
          }
        );

        if (response.data.success) {
          Toast.show({
            type: "success",
            text1: response.data.message,
          });

          dispatch(
            updateCategory({
              id: response.data.data.category._id,
              name: response.data.data.category.name,
              description: response.data.data.category.description,
            })
          );
          navigation.goBack();
        }
      } catch (error: any) {
        console.error("Error creating category:", error);
        Toast.show({
          type: "error",
          text1:
            error.message || "Something went wrong. Please try again later.",
        });
      }
    } else {
      try {
        const response = await postData<AddNewCategoryAPiResponse>(
          ENDPOINTS.createCategory,
          {
            name: categoryName,
            description: description,
          }
        );

        if (response.data.success) {
          Toast.show({
            type: "success",
            text1: response.data.message,
          });

          dispatch(
            addCategory({
              id: response.data.data.category._id,
              name: response.data.data.category.name,
              description: response.data.data.category.description,
            })
          );
          navigation.goBack();
        }
      } catch (error: any) {
        console.error("Error creating category:", error);
        Toast.show({
          type: "error",
          text1:
            error.message || "Something went wrong. Please try again later.",
        });
      }
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flex: 1, gap: moderateScale(20) }}
      >
        {/* Header */}
        <UnifiedHeader
          title={isEditing ? "Edit Category" : "Add Category"}
          onBackPress={() => navigation.goBack()}
        />

        <View style={{ paddingHorizontal: moderateScale(10) }}>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>
              Category name <Text style={styles.required}>*</Text>
            </Text>
            <TextInput
              style={styles.input}
              placeholder="e.g Face Treatment"
              placeholderTextColor={colors.greyText}
              value={categoryName}
              onChangeText={setCategoryName}
              keyboardType="default"
            />
          </View>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Description</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Explain your category in more detail..."
              placeholderTextColor={colors.greyText}
              multiline
              maxLength={200}
              value={description}
              onChangeText={setDescription}
            />
            <View style={styles.resizetext}>
              <Text style={styles.charCount}>{description.length}/200</Text>
              <CustomIcon Icon={ICONS.ResizeText} height={12} width={12} />
            </View>
          </View>
          <TouchableOpacity
            style={[
              styles.nextbtn,
              {
                opacity: !categoryName.trim() || !description.trim() ? 0.5 : 1,
              },
            ]}
            activeOpacity={0.8}
            onPress={handleSaveCategory}
            disabled={!categoryName.trim() || !description.trim()}
          >
            <Text style={styles.nextBtnText}>
              {isEditing ? "Update Category" : "Add Category"}
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default NewCategory;

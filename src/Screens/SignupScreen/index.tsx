import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Image,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { Country, CountryCode } from "react-native-country-picker-modal";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import { SignUpScreenProps } from "../../Navigation/Typings";
import { setIsAuth } from "../../Redux/slices/initialSlice";
import { useAppDispatch } from "../../Redux/store";
import { SignupApiResponse } from "../../Services/ApiResponse";
import { postData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import ThemedUploadImageOptions from "../../Utilities/Components/Modal/ThemedUploadImageOptions";
import { ThemedPhonePicker } from "../../Utilities/Components/ThemedHelpers";
import { isValidEmail, storeLocalStorageData } from "../../Utilities/Helpers";
import STORAGE_KEYS from "../../Utilities/StorageKeys";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";

const SignupScreen = ({ navigation }: SignUpScreenProps) => {
  const dispatch = useAppDispatch();
  const [email, setEmail] = useState("");
  const [user, setUser] = useState("");
  const [countryCode, setCountryCode] = useState<CountryCode>("IN");
  const [callingCode, setCallingCode] = useState("91");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [countryVisible, setCountryVisible] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [isButtonEnabled, setIsButtonEnabled] = useState(false);
  const [secureText, setSecureText] = useState(true);
  const [password, setPassword] = useState("");
  const [hasUppercase, setHasUppercase] = useState(false);
  const [hasNumber, setHasNumber] = useState(false);
  const [hasMinLength, setHasMinLength] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);
  const [profileImage, setProfileImage] = useState("");
  const [showImageOptions, setShowImageOptions] = useState(false);

  // Get theme context
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  useEffect(() => {
    setHasUppercase(/[A-Z]/.test(password));
    setHasNumber(/\d/.test(password));
    setHasMinLength(password.length >= 8);
  }, [password]);

  useEffect(() => {
    setIsButtonEnabled(
      user.trim() !== "" &&
        email.trim() !== "" &&
        password.trim() !== "" &&
        hasMinLength &&
        hasNumber &&
        hasUppercase &&
        phoneNumber.trim() !== "" &&
        isChecked
    );
  }, [user, email, password, phoneNumber, isChecked]);

  const onSelect = (country: Country) => {
    setCountryCode(country.cca2);
    setCallingCode(country.callingCode[0]);
  };

  const openPicker = () => {
    setCountryVisible(!countryVisible);
  };

  const handleImagePick = () => {
    launchImageLibrary({ mediaType: "photo", quality: 0.8 }, (response) => {
      if (response.didCancel) {
        console.log("User cancelled image picker");
      } else if (response.errorCode) {
        console.log("ImagePicker Error: ", response.errorMessage);
      } else if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        const imageData: any = {
          uri: asset.uri || "",
          width: asset.width,
          height: asset.height,
          type: asset.type,
        };
        console.log("Selected image:", imageData);
        setProfileImage(imageData.uri);
      }
      setShowImageOptions(false);
    });
  };

  const handleCameraPick = async () => {
    try {
      const result = await launchCamera({
        quality: 1,
        mediaType: "photo",
      });

      if (result.didCancel) {
        console.log("User cancelled camera");
      } else if (result.errorCode) {
        console.log("Camera error:", result.errorMessage);
      } else if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const imageData: any = {
          uri: asset.uri || "",
          width: asset.width,
          height: asset.height,
          type: asset.type,
        };
        console.log("Captured image:", imageData);
        setProfileImage(imageData.uri);
      }
      setShowImageOptions(false);
    } catch (error) {
      console.log("Camera capture failed:", error);
      Toast.show({
        type: "error",
        text1: "Camera Error",
        text2: "Failed to capture photo",
      });
    }
  };

  const handleRegister = async () => {
    if (!isValidEmail(email)) {
      Toast.show({
        type: "error",
        text1: "Please enter a valid email",
      });
      return;
    }

    try {
      setIsRegistering(true);

      // Create a new user object
      const body = {
        fullName: user,
        email: email,
        password,
        countryCode: `+${callingCode}`,
        phoneNumber,
        countryCallingCode: countryCode,
        profilePic:
          "https://images.unsplash.com/photo-1517676109075-9a94d44145d1?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      };

      const response = await postData<SignupApiResponse>(
        ENDPOINTS.signup,
        body
      );

      if (response.data.success) {
        // Save credentials for auto-login
        await storeLocalStorageData(
          STORAGE_KEYS.credentials,
          JSON.stringify({
            email: email,
            password: password,
          })
        );

        // Set auth state
        dispatch(setIsAuth(true));
        Toast.show({
          type: "success",
          text1: response.data.message,
        });
        navigation.navigate("VerificationCode", {
          email,
          token: response.data.data.verificationToken,
          isFrom: "signup",
        });
      }
    } catch (error: any) {
      console.error("Error during registration:", error);
      Toast.show({
        type: "error",
        text1: error.message || "Something went wrong. Please try again.",
      });
    } finally {
      setIsRegistering(false);
    }
  };

  return (
    <View style={styles.mainContainer}>
      <SafeAreaView
        style={{
          flex: 1,
          paddingVertical: verticalScale(20),
          paddingHorizontal: moderateScale(20),
        }}
      >
        <KeyboardAwareScrollView
          bounces={false}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.centeredView}>
            {/* <CustomIcon Icon={ICONS.LoginAppIcon} height={96} width={96} /> */}
            <Text
              style={[
                themedCommonStyles.font24W500,
                { marginTop: verticalScale(10) },
              ]}
            >
              Create a professional account
            </Text>
            <Text style={styles.subtitleText}>
              Join GlamUP and manage your business effortlessly
            </Text>
          </View>

          {/* Profile Image */}
          <View style={styles.profileImageContainer}>
            <TouchableOpacity
              onPress={() => setShowImageOptions(true)}
              style={styles.profileImageWrapper}
            >
              {profileImage ? (
                <Image
                  source={{ uri: profileImage }}
                  style={styles.profileImage}
                />
              ) : (
                <View style={styles.profileImagePlaceholder}>
                  <CustomIcon Icon={ICONS.UserIcon} width={30} height={30} />
                </View>
              )}
              <View style={styles.cameraIconContainer}>
                <CustomIcon Icon={ICONS.CameraIcon} height={16} width={16} />
              </View>
            </TouchableOpacity>
            <Text style={[styles.uploadPhotoText, { color: colors.text }]}>
              Upload Profile Photo
            </Text>
          </View>

          <View style={styles.container}>
            {/* Full Name Input */}
            <Text style={styles.labelText}>
              Full Name<Text style={{ color: colors.stateerrorbase }}>*</Text>
            </Text>
            <View style={styles.inputContainer}>
              <CustomIcon Icon={ICONS.UserIcon} width={11.67} height={13.33} />
              <TextInput
                style={styles.input}
                placeholder="Your name"
                placeholderTextColor={colors.greyText}
                value={user}
                onChangeText={setUser}
              />
            </View>

            {/* Email Input */}
            <Text style={styles.labelText}>
              Email Address
              <Text style={{ color: colors.stateerrorbase }}>*</Text>
            </Text>
            <View style={styles.inputContainer}>
              <CustomIcon Icon={ICONS.EmailIcon} width={15} height={14.17} />
              <TextInput
                style={styles.input}
                placeholder="<EMAIL>"
                placeholderTextColor={colors.greyText}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
              />
            </View>

            {/* Password Input */}
            <Text style={styles.labelText}>
              Password<Text style={{ color: colors.stateerrorbase }}>*</Text>
            </Text>
            <View style={styles.inputContainer}>
              <CustomIcon Icon={ICONS.LockIcon} width={12} height={15} />
              <TextInput
                style={styles.input}
                placeholder="••••••••••"
                placeholderTextColor={colors.greyText}
                secureTextEntry={secureText}
                value={password}
                onChangeText={setPassword}
              />
              <TouchableOpacity onPress={() => setSecureText(!secureText)}>
                <CustomIcon
                  Icon={secureText ? ICONS.EyeoffIcon : ICONS.EyeIcon}
                  width={16.23}
                  height={13.5}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.warningContainer}>
              <CustomIcon Icon={ICONS.WarningIcon} height={12} width={12} />
              <Text style={styles.warningText}>
                Must contain at least 1 uppercase letter, 1 number, min. 8
                characters.
              </Text>
            </View>

            {password.length > 0 && (
              <View
                style={{
                  paddingVertical: verticalScale(10),
                  flexDirection: "row",
                }}
              >
                <View
                  style={[
                    styles.passwordmatchingline,
                    {
                      backgroundColor: hasUppercase
                        ? colors.stateerrorbase
                        : colors.bgsoft,
                    },
                  ]}
                />
                <View
                  style={[
                    styles.passwordmatchingline1,
                    {
                      backgroundColor: hasNumber
                        ? colors.statewarningbase
                        : colors.bgsoft,
                    },
                  ]}
                />
                <View
                  style={[
                    styles.passwordmatchingline2,
                    {
                      backgroundColor: hasMinLength
                        ? colors.statesuccessbase
                        : colors.bgsoft,
                    },
                  ]}
                />
              </View>
            )}

            {password.length > 0 && (
              <View>
                <Text style={styles.validationText}>
                  <CustomIcon
                    Icon={
                      hasUppercase
                        ? ICONS.GreenrightIcon
                        : ICONS.CrossvectorIcon
                    }
                    height={12}
                    width={12}
                  />{" "}
                  At least 1 uppercase
                </Text>
                <Text style={styles.validationText}>
                  <CustomIcon
                    Icon={
                      hasNumber ? ICONS.GreenrightIcon : ICONS.CrossvectorIcon
                    }
                    height={12}
                    width={12}
                  />{" "}
                  At least 1 number
                </Text>
                <Text style={styles.validationText}>
                  <CustomIcon
                    Icon={
                      hasMinLength
                        ? ICONS.GreenrightIcon
                        : ICONS.CrossvectorIcon
                    }
                    height={12}
                    width={12}
                  />{" "}
                  At least 8 characters
                </Text>
              </View>
            )}

            {/* Phone Number Input */}
            <Text style={styles.phoneLabel}>
              Phone Number
              <Text style={{ color: colors.stateerrorbase }}>*</Text>
            </Text>

            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginTop: verticalScale(10),
              }}
            >
              <View style={styles.phoneContainer}>
                <ThemedPhonePicker
                  visible={countryVisible}
                  countryCode={countryCode}
                  onSelect={onSelect}
                  onPress={openPicker}
                />
              </View>
              <View style={styles.phoneCountry}>
                <TextInput
                  style={styles.input}
                  placeholder="00 00 0000"
                  placeholderTextColor={colors.greyText}
                  value={phoneNumber}
                  onChangeText={setPhoneNumber}
                  keyboardType="phone-pad"
                />
              </View>
            </View>

            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => setIsChecked(!isChecked)}
              activeOpacity={0.8}
            >
              {isChecked ? (
                <View
                  style={[
                    styles.checkbox,
                    {
                      backgroundColor: colors.primaryBase,
                      borderColor: colors.primaryBase,
                    },
                  ]}
                >
                  <CustomIcon
                    Icon={ICONS.CheckRightIcon}
                    height={13}
                    width={13}
                  />
                </View>
              ) : (
                <View style={styles.checkbox} />
              )}
              <Text style={styles.text}>
                I agree to the{" "}
                <Text style={styles.linkTextPrimary}>Privacy Policy</Text> &{" "}
                <Text style={styles.linkTextPrimary}>Terms of Service</Text>
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.loginButton,
                {
                  backgroundColor: isButtonEnabled
                    ? colors.primaryBase
                    : colors.bgsoft,
                },
              ]}
              disabled={!isButtonEnabled}
              onPress={handleRegister}
              activeOpacity={0.8}
            >
              {isRegistering ? (
                <ActivityIndicator size="small" color={colors.white} />
              ) : (
                <Text
                  style={[
                    styles.loginText,
                    {
                      color: isButtonEnabled ? colors.white : colors.greyText,
                    },
                  ]}
                >
                  Register
                </Text>
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.footerContainer}>
            <Text style={styles.footerText}>
              Already have an account?{` `}
              <Text
                style={styles.linkTextPrimary}
                onPress={() => {
                  navigation.navigate("LoginScreen");
                }}
              >
                Login
              </Text>
            </Text>
          </View>
        </KeyboardAwareScrollView>
      </SafeAreaView>

      {/* Image Selection Options */}
      <ThemedUploadImageOptions
        isModalVisible={showImageOptions}
        closeModal={() => setShowImageOptions(false)}
        onPressCamera={handleCameraPick}
        onPressGallery={handleImagePick}
      />
    </View>
  );
};

export default SignupScreen;

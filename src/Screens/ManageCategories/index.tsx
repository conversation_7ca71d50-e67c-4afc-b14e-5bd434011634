import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  Modal,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import {
  deleteCategories,
  setCategories,
} from "../../Redux/slices/categorySlice";
import { useAppDispatch, useAppSelector } from "../../Redux/store";
import { GetAllUserCategoriesApiResponse } from "../../Services/ApiResponse";
import { deleteData, fetchData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import { verticalScale } from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";

const ManageCategories = ({ navigation }: any) => {
  const dispatch = useAppDispatch();
  const { categories } = useAppSelector((state) => state.categories);
  const { colors, isDarkMode } = useTheme();
  const styles = useThemedStyles();

  const [isLoading, setIsLoading] = useState(false);

  const [selectAll, setSelectAll] = useState(false);
  const [search, setSearch] = useState("");
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [sortDirection, setSortDirection] = useState("asc"); // "asc" for A-Z, "desc" for Z-A
  const [localCategories, setLocalCategories] = useState(
    categories.map((cat) => ({ ...cat, checked: false }))
  );

  const getCategories = async () => {
    setIsLoading(true);
    try {
      const response = await fetchData<GetAllUserCategoriesApiResponse>(
        ENDPOINTS.getAlluserCategories
      );
      if (response.data.success) {
        dispatch(
          setCategories(
            response.data.data.categories.map((cat) => ({
              id: cat._id,
              name: cat.name,
              description: cat.description,
            }))
          )
        );
      }
    } catch (error: any) {
      console.error("Error fetching categories:", error);
      Toast.show({
        type: "error",
        text1: "Something went wrong. Please try again later.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // const deleteCategories = async () => {
  //   setIsLoading(true);
  //   try {
  //     const categoriesToDelete = localCategories
  //       .filter((category) => category.checked)
  //       .map((category) => category.id);

  //     const response = await deleteData(ENDPOINTS.deleteCategory, {
  //       categoryIds: categoriesToDelete,
  //     });
  //     if (response.data.success) {
  //       dispatch(deleteCategories(categoriesToDelete));
  //       setSelectAll(false);
  //       setIsModalVisible(false);
  //       Toast.show({
  //         type: "success",
  //         text1: response.data.message,
  //       });
  //     }
  //   } catch (error: any) {
  //     console.error("Error deleting categories:", error);
  //     Toast.show({
  //       type: "error",
  //       text1: error.message || "Something went wrong. Please try again later.",
  //     });
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };

  const toggleCheckbox = (id: string) => {
    const updatedCategories = localCategories.map((category) =>
      category.id === id
        ? { ...category, checked: !category.checked }
        : category
    );
    setLocalCategories(updatedCategories);

    const allChecked = updatedCategories.every((category) => category.checked);
    setSelectAll(allChecked);
  };

  const toggleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    const updatedCategories = localCategories.map((category) => ({
      ...category,
      checked: newSelectAll,
    }));
    setLocalCategories(updatedCategories);
  };

  // Toggle sort direction
  const toggleSortDirection = () => {
    setSortDirection(sortDirection === "asc" ? "desc" : "asc");
  };

  // Filter and sort categories
  const filteredCategories = localCategories
    .filter((category) =>
      category.name.toLowerCase().includes(search.toLowerCase())
    )
    .sort((a, b) => {
      if (sortDirection === "asc") {
        return a.name.localeCompare(b.name);
      } else {
        return b.name.localeCompare(a.name);
      }
    });

  const handleDelete = () => {
    const categoriesToDelete = localCategories
      .filter((category) => category.checked)
      .map((category) => category.id);

    dispatch(deleteCategories(categoriesToDelete));
    setSelectAll(false);
    setIsModalVisible(false);

    Toast.show({
      type: "success",
      text1: "Categories deleted successfully",
    });
  };

  const handleEditCategory = (category: any) => {
    navigation.navigate("addNewCategory", { categoryToEdit: category });
  };

  const renderItem = ({ item }: any) => (
    <View style={styles.categoryItem}>
      <TouchableOpacity
        style={styles.checkboxContainer}
        onPress={() => toggleCheckbox(item.id)}
        activeOpacity={0.8}
      >
        <View
          style={[
            styles.checkbox,
            item.checked && { backgroundColor: colors.primaryBase },
          ]}
        >
          {item.checked && (
            <View style={styles.checkboxInner}>
              <CustomIcon Icon={ICONS.CheckRightIcon} height={12} width={12} />
            </View>
          )}
        </View>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.categoryInfo}
        onPress={() => handleEditCategory(item)}
        activeOpacity={0.8}
      >
        <Text style={styles.categoryName}>{item.name}</Text>
        {item.description ? (
          <Text style={styles.categoryDescription} numberOfLines={1}>
            {item.description}
          </Text>
        ) : null}
      </TouchableOpacity>

      <TouchableOpacity
        onPress={() => handleEditCategory(item)}
        style={styles.editButton}
      >
        <CustomIcon Icon={ICONS.ArrowRightIcon} height={12} width={12} />
      </TouchableOpacity>
    </View>
  );

  useEffect(() => {
    setLocalCategories(categories.map((cat) => ({ ...cat, checked: false })));
  }, [categories]);

  useEffect(() => {
    if (categories.length === 0) {
      getCategories();
    }
  }, []);

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color={colors.primaryBase} />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <UnifiedHeader
        title="Categories"
        onBackPress={() => navigation.goBack()}
        rightButtonText="Add"
        onRightButtonPress={() => {
          navigation.navigate("addNewCategory", { categoryToEdit: null });
        }}
      />

      {categories.length > 0 ? (
        <View style={{ flex: 1, paddingHorizontal: verticalScale(10) }}>
          <View style={styles.inputContainer}>
            <CustomIcon Icon={ICONS.SearchIcon} width={15} height={14.17} />
            <TextInput
              style={styles.input}
              placeholder="Search by category name"
              placeholderTextColor={colors.greyText}
              value={search}
              onChangeText={(text) =>
                setSearch(text.trim().length === 0 ? text.trim() : text)
              }
            />
            <TouchableOpacity
              style={styles.sortButton}
              onPress={toggleSortDirection}
              activeOpacity={0.8}
            >
              <Text style={styles.sortText}>
                {sortDirection === "asc" ? "A-Z" : "Z-A"}
              </Text>
              <CustomIcon
                Icon={
                  sortDirection === "asc"
                    ? isDarkMode
                      ? ICONS.WhiteArrowUpIcon
                      : ICONS.ArrowUpIcon
                    : isDarkMode
                    ? ICONS.WhiteArrowDownIcon
                    : ICONS.ArrowDownIcon
                }
                width={16}
                height={16}
              />
            </TouchableOpacity>
          </View>

          {localCategories.some((category) => category.checked) && (
            <View style={styles.selectAllContainer}>
              <TouchableOpacity
                style={{ flexDirection: "row", alignItems: "center" }}
                onPress={toggleSelectAll}
              >
                <View
                  style={[
                    styles.checkbox,
                    selectAll && { backgroundColor: colors.primaryBase },
                  ]}
                >
                  {selectAll && (
                    <View style={styles.checkboxInner}>
                      <CustomIcon Icon={ICONS.Subtract} height={8} width={8} />
                    </View>
                  )}
                </View>
                <Text
                  style={{
                    fontSize: 12,
                    fontWeight: "400",
                    color: colors.greyText,
                  }}
                >
                  {
                    localCategories.filter((category) => category.checked)
                      .length
                  }{" "}
                  CATEGORIES SELECTED
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.deletebtn}
                onPress={() => setIsModalVisible(true)}
              >
                <CustomIcon Icon={ICONS.DeleteIcon} height={20} width={20} />
                <Text
                  style={{
                    fontSize: 14,
                    fontWeight: "400",
                    color: colors.text,
                  }}
                >
                  Delete
                </Text>
              </TouchableOpacity>
            </View>
          )}

          <FlatList
            data={filteredCategories}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            renderItem={renderItem}
            ListEmptyComponent={() => (
              <Text style={styles.emptyText}>No Categories Found</Text>
            )}
          />
        </View>
      ) : (
        <View style={styles.emptyContainer}>
          <CustomIcon Icon={ICONS.NoClientIcon} width={108} height={108} />
          <Text style={styles.noServiceText}>No categories</Text>
          <Text style={styles.noServiceSubText}>
            Add categories to organize your services better! 🎉
          </Text>
          <TouchableOpacity
            style={styles.addServiceButton}
            activeOpacity={0.8}
            onPress={() => {
              navigation.navigate("addNewCategory", { categoryToEdit: null });
            }}
          >
            <CustomIcon Icon={ICONS.SimplePlusIcon} height={20} width={20} />
            <Text style={styles.addServiceText}>Add your first category</Text>
          </TouchableOpacity>
        </View>
      )}

      <Modal transparent={true} visible={isModalVisible} animationType="fade">
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View
              style={{
                alignItems: "center",
                paddingVertical: verticalScale(10),
              }}
            >
              <CustomIcon
                Icon={ICONS.DeleteConfirmationModalIcon}
                height={96}
                width={96}
              />
            </View>
            <Text style={styles.modalTitle}>Delete categories</Text>
            <Text style={styles.modalSubtitle}>
              All Services and packages associated with the selected categories
              will also be deleted. This operation cannot be undone.
            </Text>
            <TouchableOpacity
              style={styles.confirmDeleteBtn}
              onPress={handleDelete}
            >
              <CustomIcon Icon={ICONS.WhiteDeleteIcon} width={20} height={20} />
              <Text style={styles.confirmDeleteText}>Yes, delete</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.cancelDeleteBtn}
              onPress={() => setIsModalVisible(false)}
            >
              <Text style={styles.cancelDeleteText}>No, cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default ManageCategories;

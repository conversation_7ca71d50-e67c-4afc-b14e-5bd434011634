import { StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(20),
      gap: verticalScale(20),
      backgroundColor: colors.background,
    },
    headerContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    headerLeftContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    headerRightContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
    },
    addText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.primaryBase,
    },
    inputContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderRadius: 8,
      paddingVertical: verticalScale(10),
      paddingHorizontal: moderateScale(10),
      marginVertical: verticalScale(10),
      width: "100%",
    },
    input: {
      flex: 1,
      fontSize: 14,
      fontWeight: "400",
      color: colors.text,
    },
    sortButton: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(5),
      borderRadius: 20,
      backgroundColor: isDarkMode ? colors.bglight : colors.bgsoft,
    },
    sortText: {
      fontSize: 12,
      fontWeight: "500",
      color: colors.text,
    },
    selectAllContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingVertical: verticalScale(10),
    },
    checkbox: {
      width: 20,
      height: 20,
      borderRadius: 4,
      borderWidth: 1,
      borderColor: colors.border,
      marginRight: moderateScale(10),
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: isDarkMode ? "transparent" : colors.white,
    },
    checkboxInner: {
      justifyContent: "center",
      alignItems: "center",
    },
    deletebtn: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
      backgroundColor: isDarkMode ? colors.bglight : colors.bgsoft,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(5),
      borderRadius: 20,
    },
    categoryItem: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      paddingHorizontal: moderateScale(20),
      paddingVertical: verticalScale(15),
      marginVertical: verticalScale(5),
      borderRadius: 8,
    },
    checkboxContainer: {
      marginRight: moderateScale(10),
    },
    categoryInfo: {
      flex: 1,
    },
    categoryName: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    categoryDescription: {
      fontSize: 12,
      fontWeight: "400",
      marginTop: verticalScale(5),
      color: colors.greyText,
    },
    editButton: {
      padding: moderateScale(5),
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: moderateScale(20),
    },
    noServiceText: {
      fontSize: 24,
      fontWeight: "500",
      marginTop: verticalScale(20),
      color: colors.text,
    },
    noServiceSubText: {
      fontSize: 14,
      fontWeight: "400",
      textAlign: "center",
      marginTop: verticalScale(10),
      marginBottom: verticalScale(20),
      color: colors.greyText,
    },
    addServiceButton: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors.primaryBase,
      paddingHorizontal: moderateScale(20),
      paddingVertical: verticalScale(10),
      borderRadius: 25,
      gap: moderateScale(10),
    },
    addServiceText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
    modalContainer: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
    },
    modalContent: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderRadius: 12,
      padding: moderateScale(20),
      width: "80%",
      alignItems: "center",
    },
    modalTitle: {
      fontSize: 24,
      fontWeight: "500",
      color: colors.text,
      textAlign: "center",
    },
    modalSubtitle: {
      fontSize: 16,
      fontWeight: "400",
      color: colors.text,
      textAlign: "center",
      marginVertical: 2,
    },
    confirmDeleteBtn: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors.stateerrorbase,
      paddingHorizontal: moderateScale(20),
      paddingVertical: verticalScale(10),
      borderRadius: 25,
      marginTop: verticalScale(20),
      gap: moderateScale(10),
    },
    confirmDeleteText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
    cancelDeleteBtn: {
      paddingHorizontal: moderateScale(20),
      paddingVertical: verticalScale(10),
      marginTop: verticalScale(10),
    },
    cancelDeleteText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.text,
    },
    emptyText: {
      textAlign: "center",
      color: colors.text,
    },
  });
};

import { StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
} from "../../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../../Utilities/ThemeContext";

export const useThemedPackageServicesStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    item: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      padding: 10,
      borderRadius: 10,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 10,
      borderWidth: 1,
      borderColor: colors.border,
    },
    serviceName: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    serviceDetails: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.greyText,
    },
    addServiceButton: {
      borderWidth: 1,
      borderRadius: 10,
      borderColor: colors.border,
      marginTop: verticalScale(20),
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      paddingVertical: verticalScale(10),
      paddingHorizontal: verticalScale(10),
      alignSelf: "center",
    },
    addServiceText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.text,
    },
    emptyText: {
      fontSize: 14,
      fontWeight: "400",
      textAlign: "center",
      color: colors.text,
      marginVertical: 20,
    },
    // Modal styles
    modalOverlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
    },
    modalContainer: {
      backgroundColor: colors.background,
      borderRadius: 15,
      padding: moderateScale(20),
      marginHorizontal: moderateScale(20),
      minWidth: moderateScale(280),
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: "600",
      color: colors.text,
      textAlign: "center",
      marginBottom: verticalScale(10),
    },
    modalMessage: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.greyText,
      textAlign: "center",
      marginBottom: verticalScale(20),
      lineHeight: 20,
    },
    modalButtons: {
      flexDirection: "row",
      gap: moderateScale(10),
    },
    modalButton: {
      flex: 1,
      paddingVertical: verticalScale(12),
      borderRadius: 8,
      alignItems: "center",
    },
    cancelButton: {
      backgroundColor: colors.bgsoft,
      borderWidth: 1,
      borderColor: colors.border,
    },
    deleteButton: {
      backgroundColor: colors.stateerrorbase,
    },
    cancelButtonText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    deleteButtonText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
  });
};

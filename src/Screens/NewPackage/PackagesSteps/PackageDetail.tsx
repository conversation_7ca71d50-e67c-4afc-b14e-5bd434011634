import React, { useState } from "react";
import {
  FlatList,
  Modal,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { useAppSelector } from "../../../Redux/store";
import { verticalScale } from "../../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedPackageDetailStyles } from "./themedPackageDetailStyles";
import useTranslation from "../../../Localization/useTranslation";

const PackageDetail = ({
  description,
  setDescription,
  packageName,
  setPackageName,
  category,
  setCategory,
}: any) => {
  const [isCategoryModalVisible, setIsCategoryModalVisible] = useState(false);
  const { colors } = useTheme();
  const styles = useThemedPackageDetailStyles();
  const themedCommonStyles = useThemedCommonStyles();
  const { packages: packagesTranslation, categories: categoriesTranslation } =
    useTranslation();

  const { categories } = useAppSelector((state) => state.categories);

  const handleCategorySelect = (selectedCategory: string) => {
    setCategory(selectedCategory);
    setIsCategoryModalVisible(false);
  };

  const renderCategoryItem = ({
    item,
  }: {
    item: { id: string; name: string };
  }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => handleCategorySelect(item.id)}
    >
      <Text style={themedCommonStyles.font14}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>
            {packagesTranslation("packageName")}{" "}
            <Text style={styles.required}>*</Text>
          </Text>
          <TextInput
            style={styles.input}
            placeholder={packagesTranslation("packageNamePlaceholder")}
            placeholderTextColor={colors.greyText}
            value={packageName}
            onChangeText={setPackageName}
            keyboardType="default"
          />
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>
            {categoriesTranslation("category")}{" "}
            <Text style={styles.required}>*</Text>
          </Text>
          <TouchableOpacity
            style={styles.categorySelector}
            onPress={() => setIsCategoryModalVisible(true)}
          >
            <TextInput
              style={[styles.input, { flex: 1 }]}
              placeholder={categoriesTranslation("selectCategory")}
              placeholderTextColor={colors.greyText}
              value={categories.find((c) => c.id === category)?.name}
              editable={false}
            />
            <CustomIcon Icon={ICONS.DropdownIcon} height={12} width={12} />
          </TouchableOpacity>
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>
            {packagesTranslation("packageDescription")}
          </Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder={packagesTranslation("packageDescriptionPlaceholder")}
            placeholderTextColor={colors.greyText}
            multiline
            value={description}
            onChangeText={setDescription}
          />
          <View style={styles.resizetext}>
            <Text style={styles.charCount}>{description.length}/200 </Text>
            <CustomIcon Icon={ICONS.ResizeText} height={12} width={12} />
          </View>
        </View>
      </KeyboardAwareScrollView>

      {/* Category Selection Modal */}
      <Modal
        transparent={true}
        visible={isCategoryModalVisible}
        animationType="fade"
        onRequestClose={() => setIsCategoryModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={themedCommonStyles.font24W500}>
                {categoriesTranslation("selectCategory")}
              </Text>
              <TouchableOpacity
                onPress={() => setIsCategoryModalVisible(false)}
              >
                <CustomIcon Icon={ICONS.CrossIcon} height={20} width={20} />
              </TouchableOpacity>
            </View>
            <FlatList
              data={categories}
              renderItem={renderCategoryItem}
              keyExtractor={(item) => item.id}
              style={{ maxHeight: verticalScale(300) }}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default PackageDetail;

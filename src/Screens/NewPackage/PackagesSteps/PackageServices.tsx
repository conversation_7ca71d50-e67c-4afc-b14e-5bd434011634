import React, { useRef } from "react";
import {
  FlatList,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import AddServiceRbSheet from "../../../Utilities/Components/Modal/AddServiceRbSheet";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedCommonStyles } from "../../../Utilities/Styles/themedCommonStyles";
import { getCurrencySymbol } from '../../../Utilities/currencyUtils';
import { useThemedPackageServicesStyles } from "./themedPackageServicesStyles";

const PackageServices = ({ services, setServices }: any) => {
  const refRBSheet = useRef<any>(null);
  const { colors } = useTheme();
  const styles = useThemedPackageServicesStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const handleDelete = (id: string) => {
    setServices(
      services.filter((service: any) => service.id !== id && service._id !== id)
    );
  };

  const renderItem = ({ item }: any) => (
    <View style={styles.item}>
      <View>
        <Text style={styles.serviceName}>{item.name || item.businessName}</Text>
        <Text style={styles.serviceDetails}>
          {`${getCurrencySymbol(item.currency)}${item.price} · ${
            typeof item.duration === "number"
              ? `${item.duration} min`
              : item.duration
          }`}
        </Text>
      </View>
      <TouchableOpacity onPress={() => handleDelete(item.id || item._id)}>
        <CustomIcon Icon={ICONS.DeleteIcon} height={20} width={20} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View>
      {services && services.length > 0 ? (
        <FlatList
          data={services}
          keyExtractor={(item) => item._id || item.id}
          renderItem={renderItem}
        />
      ) : (
        <Text style={styles.emptyText}>
          No services added yet. Click the button below to add services to your
          package.
        </Text>
      )}
      <TouchableOpacity
        style={styles.addServiceButton}
        activeOpacity={0.8}
        onPress={() => {
          refRBSheet.current && refRBSheet?.current.open();
        }}
      >
        <Text style={styles.addServiceText}>Add service</Text>
      </TouchableOpacity>
      <AddServiceRbSheet
        refRBSheet={refRBSheet}
        selectedServices={services}
        setSelectedServices={setServices}
      />
    </View>
  );
};

export default PackageServices;

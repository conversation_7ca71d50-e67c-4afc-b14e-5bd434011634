import React, { useRef, useState } from "react";
import { FlatList, Modal, Text, TouchableOpacity, View } from "react-native";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import AddServiceRbSheet from "../../../Utilities/Components/Modal/AddServiceRbSheet";
import { getCurrencySymbol } from "../../../Utilities/currencyUtils";
import { useThemedPackageServicesStyles } from "./themedPackageServicesStyles";
import useTranslation from "../../../Localization/useTranslation";

const PackageServices = ({
  services,
  setServices,
  selectedCategoryId,
}: any) => {
  const refRBSheet = useRef<any>(null);
  const styles = useThemedPackageServicesStyles();
  const {
    packages: packagesTranslation,
    services: servicesTranslation,
    common,
  } = useTranslation();

  // Confirmation modal state
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState<any>(null);

  const confirmDeleteService = (service: any) => {
    setServiceToDelete(service);
    setShowDeleteModal(true);
  };

  const handleDelete = () => {
    if (!serviceToDelete) return;

    const serviceId =
      serviceToDelete.id || serviceToDelete._id || serviceToDelete.serviceId;
    setServices(
      services.filter(
        (service: any) =>
          service.id !== serviceId &&
          service._id !== serviceId &&
          service.serviceId !== serviceId
      )
    );

    setShowDeleteModal(false);
    setServiceToDelete(null);
  };

  const renderItem = ({ item }: any) => (
    <View style={styles.item}>
      <View>
        <Text style={styles.serviceName}>{item.name}</Text>
        <Text style={styles.serviceDetails}>
          {`${getCurrencySymbol(item.currency)}${item.price} · ${
            item.duration
          }`}
        </Text>
      </View>
      <TouchableOpacity onPress={() => confirmDeleteService(item)}>
        <CustomIcon Icon={ICONS.DeleteIcon} height={20} width={20} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View>
      {services && services.length > 0 ? (
        <FlatList
          data={services}
          keyExtractor={(item) => item.id || item._id || item.serviceId}
          renderItem={renderItem}
        />
      ) : (
        <Text style={styles.emptyText}>
          {packagesTranslation("noServicesAdded")}
        </Text>
      )}
      <TouchableOpacity
        style={styles.addServiceButton}
        activeOpacity={0.8}
        onPress={() => {
          refRBSheet.current && refRBSheet?.current.open();
        }}
      >
        <Text style={styles.addServiceText}>
          {servicesTranslation("addService")}
        </Text>
      </TouchableOpacity>
      <AddServiceRbSheet
        refRBSheet={refRBSheet}
        selectedServices={services}
        setSelectedServices={setServices}
        selectedCategoryId={selectedCategoryId}
      />

      {/* Delete Service Confirmation Modal */}
      <Modal
        visible={showDeleteModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDeleteModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>
              {packagesTranslation("removeServiceTitle")}
            </Text>
            <Text style={styles.modalMessage}>
              {packagesTranslation("removeServiceConfirm").replace(
                "{serviceName}",
                serviceToDelete?.name || ""
              )}
            </Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowDeleteModal(false)}
              >
                <Text style={styles.cancelButtonText}>{common("cancel")}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.deleteButton]}
                onPress={handleDelete}
              >
                <Text style={styles.deleteButtonText}>
                  {packagesTranslation("removeService")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default PackageServices;

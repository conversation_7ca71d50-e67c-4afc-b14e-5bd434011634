import React, { useCallback, useEffect, useState } from "react";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { addPackage, updatePackage } from "../../Redux/slices/packagesSlice";
import { useAppDispatch } from "../../Redux/store";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import PackageDetail from "./PackagesSteps/PackageDetail";
import PackageDuration from "./PackagesSteps/PackageDuration";
import PackageServices from "./PackagesSteps/PackageServices";
import { useThemedStyles } from "./themedStyles";

const tabs = [
  {
    id: "PackageDetails",
    label: "Details",
    selectedIcon: ICONS.SelectedServicesDetail,
    unselectedIcon: ICONS.ServicesDetail,
  },
  {
    id: "PackageServices",
    label: "Services",
    selectedIcon: ICONS.SelectedServices,
    unselectedIcon: ICONS.PackageServices,
  },
  {
    id: "PackageDuration",
    label: "Duration & Price",
    selectedIcon: ICONS.SelectedCurrency,
    unselectedIcon: ICONS.CurrencyIcon,
  },
];

const NewPackage = ({ navigation, route }: any) => {
  const dispatch = useAppDispatch();
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const packageToEdit = route.params?.packageToEdit;
  const isEditing = !!packageToEdit;

  const [activeIndex, setActiveIndex] = useState(0);
  const [description, setDescription] = useState("");
  const [PackageName, setPackageName] = useState("");
  const [Category, setCategory] = useState("");
  const [services, setServices] = useState([]);
  const [duration, setDuration] = useState("15 min");
  const [customMinutes, setCustomMinutes] = useState("");
  const [isCustomDuration, setIsCustomDuration] = useState(false);
  const [priceType, setPriceType] = useState("Fixed price");
  const [currency, setCurrency] = useState("EUR");
  const [price, setPrice] = useState(0);

  // Prefill form when editing
  useEffect(() => {
    if (packageToEdit) {
      // Use API data structure only
      setDescription(packageToEdit.description || "");
      setPackageName(packageToEdit.name || "");
      setCategory(packageToEdit.categoryName || "");

      // Handle services - Convert API services to expected format
      if (packageToEdit.services && packageToEdit.services.length > 0) {
        const formattedServices = packageToEdit.services.map(
          (service: any) => ({
            id: service.serviceId || service._id, // Use serviceId from API response
            _id: service.serviceId || service._id, // Also set _id for compatibility
            businessName: service.name,
            name: service.name,
            category: packageToEdit.categoryName, // Use package's category since service might not have it
            description: service.description || "",
            duration: `${service.duration} min`,
            price: service.price,
            currency: packageToEdit.currency, // Use package's currency since service might not have it
            priceType: packageToEdit.priceType || "Fixed price",
          })
        );
        console.log("Formatted services for package:", formattedServices);
        setServices(formattedServices);
      } else {
        setServices([]);
      }

      // Handle duration - API returns number in minutes
      const durationInMinutes = packageToEdit.duration;
      if (durationInMinutes === 15) {
        setDuration("15 min");
      } else if (durationInMinutes === 30) {
        setDuration("30 min");
      } else if (durationInMinutes === 45) {
        setDuration("45 min");
      } else if (durationInMinutes === 60) {
        setDuration("1 hour");
      } else if (durationInMinutes === 90) {
        setDuration("1 hour 30 min");
      } else if (durationInMinutes === 120) {
        setDuration("2 hours");
      } else {
        setDuration("Custom");
        setCustomMinutes(durationInMinutes.toString());
        setIsCustomDuration(true);
      }

      setPriceType(packageToEdit.priceType || "Fixed price");
      setCurrency(packageToEdit.currency || "EUR");
      setPrice(packageToEdit.finalPrice || packageToEdit.price || 0);
    }
  }, [packageToEdit]);

  const handleGoBack = () => {
    navigation.goBack();
  };

  const renderPackageTabs = () => {
    switch (activeIndex) {
      case 0:
        return (
          <PackageDetail
            key="PackageDetails"
            description={description}
            setDescription={setDescription}
            packageName={PackageName}
            setPackageName={setPackageName}
            category={Category}
            setCategory={setCategory}
          />
        );
      case 1:
        return (
          <PackageServices
            key="PackageServices"
            services={services}
            setServices={setServices}
          />
        );
      case 2:
        return (
          <PackageDuration
            key="PackageDuration"
            duration={duration}
            setDuration={setDuration}
            customMinutes={customMinutes}
            setCustomMinutes={setCustomMinutes}
            isCustomDuration={isCustomDuration}
            setIsCustomDuration={setIsCustomDuration}
            priceType={priceType}
            setPriceType={setPriceType}
            currency={currency}
            setCurrency={setCurrency}
            price={price}
            setPrice={setPrice}
          />
        );
    }
  };

  const isNextButtonDisabled = useCallback(() => {
    switch (activeIndex) {
      case 0:
        return PackageName.trim() === "" || Category.trim() === "";
      case 1:
        return services.length === 0;
      case 2:
        return price === 0;
    }
  }, [activeIndex, PackageName, Category, services, price]);

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <UnifiedHeader
          title={isEditing ? "Edit package" : "New package"}
          onBackPress={handleGoBack}
        />
        {/* Scrollable Horizontal Tab Navigation */}
        <View
          style={{
            paddingVertical: verticalScale(20),
            paddingHorizontal: moderateScale(15),
          }}
        >
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View
              style={{
                flexDirection: "row",
                borderBottomWidth: 1,
                borderBottomColor: colors.bgsoft,
                gap: moderateScale(15),
              }}
            >
              {tabs.map((item, index) => {
                const isSelected = activeIndex === index;
                return (
                  <TouchableOpacity
                    key={item.id}
                    activeOpacity={1}
                    disabled
                    onPress={() => setActiveIndex(index)}
                    style={{
                      alignItems: "center",
                      paddingRight: moderateScale(10),
                      paddingVertical: 10,
                      borderColor: isSelected
                        ? colors.primaryBase
                        : colors.bgsoft,
                      borderBottomWidth: isSelected ? 1.4 : 0.2,
                    }}
                  >
                    <View
                      style={{ flexDirection: "row", gap: moderateScale(5) }}
                    >
                      <CustomIcon
                        Icon={
                          isSelected ? item.selectedIcon : item.unselectedIcon
                        }
                        height={20}
                        width={20}
                      />
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: "400",
                          color: isSelected
                            ? colors.primaryBase
                            : colors.greyText,
                        }}
                      >
                        {item.label}
                      </Text>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>
          </ScrollView>
        </View>
        ;{/* Render Tab Content */}
        <View style={styles.contentContainer}>{renderPackageTabs()}</View>;
        {/* Next / Save Package Button */}
        <TouchableOpacity
          style={[
            styles.nextbtn,
            {
              opacity: isNextButtonDisabled() ? 0.6 : 1,
            },
          ]}
          disabled={isNextButtonDisabled()}
          onPress={() => {
            if (activeIndex < tabs.length - 1) {
              setActiveIndex(activeIndex + 1);
            } else {
              // Prepare package data
              const packageData = {
                packageName: PackageName,
                category: Category,
                description: description,
                duration: duration,
                customMinutes: customMinutes,
                isCustomDuration: isCustomDuration,
                priceType: priceType,
                currency: currency,
                price: price,
                services: services,
              };

              // Dispatch the appropriate action based on whether we're editing or creating
              if (isEditing) {
                dispatch(
                  updatePackage({
                    id: packageToEdit._id,
                    ...packageData,
                  })
                );
              } else {
                dispatch(addPackage(packageData));
              }

              // Navigate back to the services list
              navigation.navigate("serviceList");
            }
          }}
        >
          <Text style={styles.nextBtnText}>
            {activeIndex === tabs.length - 1
              ? isEditing
                ? "Save Package"
                : "Add Package"
              : "Next"}
          </Text>
          {activeIndex !== tabs.length - 1 && (
            <CustomIcon Icon={ICONS.WhiteArrow} height={12} width={12} />
          )}
        </TouchableOpacity>
        ;
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default NewPackage;

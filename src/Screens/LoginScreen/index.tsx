import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Linking,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import { LoginScreenProps } from "../../Navigation/Typings";
import { setIsbusineesSetup } from "../../Redux/slices/initialSlice";
import { useAppDispatch, useAppSelector } from "../../Redux/store";
import { LoginApiResponse } from "../../Services/ApiResponse";
import { postData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import {
  deleteLocalStorageData,
  getLocalStorageData,
  storeLocalStorageData,
} from "../../Utilities/Helpers";
import STORAGE_KEYS from "../../Utilities/StorageKeys";
import {
  moderateScale,
  verticalScale,
  width,
} from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";

// Storage keys
const LoginScreen = ({ navigation }: LoginScreenProps) => {
  const dispatch = useAppDispatch();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [secureText, setSecureText] = useState(true);
  const [isChecked, setIsChecked] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Get theme context
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  // Load saved credentials when component mounts
  useEffect(() => {
    const loadSavedCredentials = async () => {
      try {
        const savedCredentialsJson = await getLocalStorageData(
          STORAGE_KEYS.credentials
        );

        if (savedCredentialsJson) {
          const savedCredentials = JSON.parse(savedCredentialsJson);
          setEmail(savedCredentials.email || "");
          setPassword(savedCredentials.password || "");
          setIsChecked(true); // Set remember me to checked
        }
      } catch (error) {
        console.error("Error loading saved credentials:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSavedCredentials();
  }, []);

  // Button should be enabled if either email or password has text
  const isButtonEnabled = email.trim() !== "" && password.trim() !== "";

  // Save credentials to AsyncStorage
  const saveCredentials = async () => {
    try {
      if (isChecked) {
        // Save credentials only if "Remember Me" is checked
        const credentials = {
          email,
          password,
        };
        await storeLocalStorageData(
          STORAGE_KEYS.credentials,
          JSON.stringify(credentials)
        );
      } else {
        // Clear saved credentials if "Remember Me" is unchecked
        await deleteLocalStorageData(STORAGE_KEYS.credentials);
      }
    } catch (error) {
      console.error("Error saving credentials:", error);
    }
  };

  // This function would be used when implementing logout functionality
  // It's defined here for reference but will be implemented in the logout screen
  // Example implementation:
  /*
  const clearCredentials = async () => {
    try {
      await AsyncStorage.removeItem(CREDENTIALS_KEY);
    } catch (error) {
      console.error("Error clearing credentials:", error);
    }
  };
  */

  const handleLogin = async () => {
    if (isButtonEnabled) {
      setIsLoading(true);
      try {
        const response = await postData<LoginApiResponse>(ENDPOINTS.login, {
          email,
          password,
          fcmToken: "37248",
        });
        if (response.data.success) {
          await storeLocalStorageData(
            STORAGE_KEYS.token,
            response.data.data.token
          );

          // Save credentials if "Remember Me" is checked
          await saveCredentials();

          if (response.data.data.businessRole.length > 0) {
            navigation.replace("mainStack", {
              screen: "bottomTabs",
              params: { screen: "overView" },
            });
          } else {
            // Set business setup to false and navigate to setup screen
            dispatch(setIsbusineesSetup(false));
            navigation.navigate("setupStack", { screen: "SetupBusiness" });
          }
        }

        // Navigate based on business setup status
      } catch (error: any) {
        console.error("Error during login:", error);
        Toast.show({
          type: "error",
          text1: error.message || "Something went wrong. Please try again.",
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <View style={styles.mainContainer}>
      <SafeAreaView style={styles.safeAreaContainer}>
        <KeyboardAwareScrollView
          bounces={false}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.centeredView}>
            <CustomIcon Icon={ICONS.LoginAppIcon} height={96} width={96} />
            <Text
              style={[
                themedCommonStyles.font20main,
                { marginTop: verticalScale(10) },
              ]}
            >
              Access your GlamUP account
            </Text>
            <Text
              style={[
                themedCommonStyles.font16400,
                { marginTop: verticalScale(2) },
              ]}
            >
              Your wellness journey starts here
            </Text>

            <View style={styles.container}>
              {/* Email Input */}
              <Text style={styles.labelText}>
                Email Address
                <Text style={{ color: colors.stateerrorbase }}>*</Text>
              </Text>
              <View style={styles.inputContainer}>
                <CustomIcon Icon={ICONS.EmailIcon} width={15} height={14.17} />
                <TextInput
                  style={styles.input}
                  placeholder="<EMAIL>"
                  placeholderTextColor={colors.greyText}
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                />
              </View>

              {/* Password Input */}
              <Text style={styles.labelText}>
                Password<Text style={{ color: colors.stateerrorbase }}>*</Text>
              </Text>
              <View style={styles.inputContainer}>
                <CustomIcon Icon={ICONS.LockIcon} width={12} height={15} />
                <TextInput
                  style={styles.input}
                  placeholder="••••••••••"
                  placeholderTextColor={colors.greyText}
                  secureTextEntry={secureText}
                  value={password}
                  onChangeText={setPassword}
                />
                <TouchableOpacity
                  onPress={() => setSecureText(!secureText)}
                  style={{
                    paddingHorizontal: moderateScale(5),
                    height: "100%",
                    flexDirection: "row",
                    alignItems: "center",
                  }}
                >
                  <CustomIcon
                    Icon={secureText ? ICONS.EyeoffIcon : ICONS.EyeIcon}
                    width={16.23}
                    height={13.5}
                  />
                </TouchableOpacity>
              </View>

              {/* Keep me logged in and Forgot Password */}
              <View style={styles.row}>
                <TouchableOpacity
                  style={styles.checkboxContainer}
                  onPress={() => setIsChecked(!isChecked)}
                  activeOpacity={0.8}
                >
                  {isChecked ? (
                    <View
                      style={[
                        styles.checkbox,
                        {
                          backgroundColor: colors.primaryBase,
                          borderColor: colors.primaryBase,
                          alignItems: "center",
                          justifyContent: "center",
                        },
                      ]}
                    >
                      <CustomIcon
                        Icon={ICONS.CheckRightIcon}
                        height={12}
                        width={12}
                      />
                    </View>
                  ) : (
                    <View style={styles.checkbox} />
                  )}
                  <Text style={styles.text}>Keep me logged in</Text>
                </TouchableOpacity>

                <Text
                  style={styles.linkText}
                  onPress={() => navigation.navigate("PasswordReset")}
                >
                  Forgot password?
                </Text>
              </View>

              {/* Login Button with Dynamic Color */}
              <TouchableOpacity
                style={[
                  styles.loginButton,
                  {
                    backgroundColor: isButtonEnabled
                      ? colors.primaryBase
                      : colors.bgsoft,
                  },
                ]}
                disabled={!isButtonEnabled || isLoading}
                onPress={handleLogin}
                activeOpacity={0.8}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color={colors.white} />
                ) : (
                  <Text
                    style={[
                      styles.loginText,
                      {
                        color: isButtonEnabled ? colors.white : colors.greyText,
                      },
                    ]}
                  >
                    Login
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
          <View style={{ width: width * 0.7, alignSelf: "center" }}>
            <Text
              style={{
                color: colors.greyText,
                fontSize: 14, // Changed from 12 to 14
                fontWeight: "700",
                textAlign: "center",
                lineHeight: 20,
              }}
            >
              Notice: If you are a client, please switch to the{" "}
              <Text
                onPress={() => {
                  // Handle client app link or action here
                  Linking.openURL(
                    "https://play.google.com/store/apps/details?id=com.gamup"
                  );
                }}
                style={{
                  color: colors.text,
                  fontSize: 14,
                  fontWeight: "500",
                  textDecorationLine: "underline",
                }}
              >
                GlamUP Client App
              </Text>{" "}
              for the best experience.
            </Text>
          </View>
        </KeyboardAwareScrollView>

        <View>
          <Text style={styles.footerText}>
            Don't have a professional account?{` `}
            <Text
              style={styles.linkTextPrimary}
              onPress={() => {
                navigation.navigate("SignupScreen");
              }}
            >
              Register
            </Text>
          </Text>
        </View>
      </SafeAreaView>
    </View>
  );
};

export default LoginScreen;

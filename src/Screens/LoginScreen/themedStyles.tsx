import { StyleSheet, Platform } from 'react-native';
import { moderateScale, width } from '../../Utilities/Styles/responsiveSize';
import { useTheme } from '../../Utilities/ThemeContext';

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      padding: 20,
      justifyContent: "center",
      marginTop: 20,
      width: width,
    },
    inputContainer: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      padding: Platform.OS == "ios" ? 10 : 0,
      paddingHorizontal: 10,
      marginBottom: moderateScale(15),
      marginTop: 10,
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    input: {
      flex: 1,
      marginHorizontal: moderateScale(10),
      color: colors.text,
    },
    row: {
      flex: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: moderateScale(10),
      marginBottom: moderateScale(20),
    },
    checkboxContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    checkbox: {
      width: 16,
      height: 16,
      borderWidth: 2,
      borderColor: colors.border,
      backgroundColor: isDarkMode ? "transparent" : colors.white,
      borderRadius: 4,
      marginRight: 8,
    },
    text: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "400",
    },
    linkText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
      textDecorationLine: "underline",
    },
    loginButton: {
      backgroundColor: colors.bgsoft,
      padding: 10,
      borderRadius: 10,
      alignItems: "center",
    },
    loginText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    mainContainer: {
      flex: 1,
      backgroundColor: colors.background,
      paddingBottom: moderateScale(40),
    },
    safeAreaContainer: {
      flex: 1,
      alignItems: "center",
      backgroundColor: colors.background,
    },
    centeredView: {
      alignItems: "center",
    },
    titleText: {
      color: colors.text,
      fontSize: 20,
      fontWeight: "500",
    },
    subtitleText: {
      color: colors.text,
      fontSize: 16,
      fontWeight: "400",
    },
    labelText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    footerText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    linkTextPrimary: {
      color: colors.primaryBase,
      fontSize: 14,
      fontWeight: "500",
      textDecorationLine: "underline",
    },
    noticeText: {
      color: colors.greyText,
      fontSize: 12,
      fontWeight: "700",
      textAlign: "center",
    },
    languageSelector: {
      position: "absolute",
      top: 50,
      right: 20,
      zIndex: 1000,
    },
    languageButton: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 8,
      gap: 8,
    },
    languageText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "500",
    },
    languageDropdown: {
      position: "absolute",
      top: "100%",
      right: 0,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      marginTop: 4,
      minWidth: 100,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    languageOption: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    languageOptionText: {
      color: colors.text,
      fontSize: 14,
      fontWeight: "400",
    },
  });
};

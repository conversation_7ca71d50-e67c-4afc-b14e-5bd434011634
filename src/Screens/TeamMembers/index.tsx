import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  Image,
  Modal,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import UnifiedHeader from "../../Components/UnifiedHeader";
import { ManageTeamMeberProps } from "../../Navigation/Typings";
import { deleteMembers, setMembers } from "../../Redux/slices/memberSlice";
import { useAppDispatch, useAppSelector } from "../../Redux/store";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";
import { verticalScale } from "../../Utilities/Styles/responsiveSize";
import Toast from "react-native-toast-message";
import { deleteData, fetchData } from "../../Services/ApiService";
import { GetAllTeamMembersApiResponse } from "../../Services/ApiResponse";
import ENDPOINTS from "../../Services/EndPoints";

const TeamMembers = ({ navigation }: ManageTeamMeberProps) => {
  const [selectAll, setSelectAll] = useState(false);
  const [search, setSearch] = useState("");
  const [isModalVisible, setIsModalVisible] = useState(false);

  const dispatch = useAppDispatch();
  const { members } = useAppSelector((state) => state.members);
  const { colors } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const [isLoading, setIsLoading] = useState(false);

  const toggleCheckbox = (id: string) => {
    const updatedClients = members.map((client) =>
      client.id === id ? { ...client, checked: !client.checked } : client
    );
    dispatch(setMembers(updatedClients));

    const allChecked = updatedClients.every((client) => client.checked);
    setSelectAll(allChecked);
  };

  const toggleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    const updatedClients = members.map((client) => ({
      ...client,
      checked: newSelectAll,
    }));
    dispatch(setMembers(updatedClients));
  };

  const filteredMembers = members.filter((client) =>
    `${client.name} ${client.email}`
      .toLowerCase()
      .includes(search.toLowerCase())
  );

  const handleGoBack = () => {
    navigation.goBack();
  };

  const renderItem = ({ item, index }: any) => (
    <View style={[styles.memberItem, item.checked && styles.memberItemChecked]}>
      <TouchableOpacity
        onPress={() => toggleCheckbox(item.id)}
        style={[
          styles.checkbox,
          {
            backgroundColor: item.checked ? colors.primaryBase : "transparent",
            borderWidth: 1,
            borderColor: item.checked ? "transparent" : colors.border,
          },
        ]}
      >
        {item.checked && (
          <View style={styles.checkboxInner}>
            <CustomIcon Icon={ICONS.CheckRightIcon} height={12} width={12} />
          </View>
        )}
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          navigation.navigate("addTeamMember", {
            memberData: item,
          });
        }}
        style={{ flexDirection: "row", alignItems: "center", flex: 1 }}
      >
        <Image source={{ uri: item.image }} style={styles.profileImage} />
        <View style={styles.memberInfo}>
          <Text style={styles.memberName}>{item.name}</Text>
          <Text style={styles.memberEmail}>{item.email}</Text>
        </View>
      </TouchableOpacity>
    </View>
  );

  const getAllTeammembers = async () => {
    setIsLoading(true);
    try {
      const response = await fetchData<GetAllTeamMembersApiResponse>(
        ENDPOINTS.getAllTeamMembers
      );
      if (response.data.success) {
        dispatch(
          setMembers(
            response.data.data.teamMembers.map((member) => ({
              id: member._id,
              name: member.name,
              email: member.email,
              number: member.phoneNumber,
              countryCode: member.countryCode,
              callingCode: member.countryCallingCode,
              image: member.profilePicture,
              checked: false,
              birthday: member.birthday,
              gender:
                member.gender.charAt(0).toUpperCase() + member.gender.slice(1),
            }))
          )
        );
      }
    } catch (error: any) {
      console.error("Error fetching team members:", error);
      Toast.show({
        type: "error",
        text1: error.message || "Something went wrong. Please try again later.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const deleteTeamMembers = async () => {
    setIsLoading(true);
    try {
      const membersToDelete = members
        .filter((client) => client.checked)
        .map((client) => client.id);

      const response = await deleteData(ENDPOINTS.deleteTeamMember, {
        teamIds: membersToDelete,
      });
      if (response.data.success) {
        dispatch(deleteMembers(membersToDelete));
        setSelectAll(false);
        setIsModalVisible(false);
        Toast.show({
          type: "success",
          text1: response.data.message,
        });
      }
    } catch (error: any) {
      console.error("Error deleting team members:", error);
      Toast.show({
        type: "error",
        text1: error.message || "Something went wrong. Please try again later.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getAllTeammembers();
  }, []);

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <UnifiedHeader
          title="Team members"
          onBackPress={handleGoBack}
          rightButtonText="Add"
          onRightButtonPress={() => {
            navigation.navigate("addTeamMember", {});
          }}
        />
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color={colors.primaryBase} />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Unified Header */}
      <UnifiedHeader
        title="Team members"
        onBackPress={handleGoBack}
        rightButtonText="Add"
        onRightButtonPress={() => {
          navigation.navigate("addTeamMember", {});
        }}
      />

      <View style={{ paddingHorizontal: verticalScale(10), flex: 1 }}>
        {/* Select All and Delete Button */}

        {/* Members List */}
        {members.length > 0 ? (
          <FlatList
            ListHeaderComponent={() => (
              <>
                <View style={styles.inputContainer}>
                  <CustomIcon
                    Icon={ICONS.SearchIcon}
                    width={15}
                    height={14.17}
                  />
                  <TextInput
                    style={styles.input}
                    placeholder="Search by name or email"
                    placeholderTextColor={colors.greyText}
                    value={search}
                    onChangeText={setSearch}
                  />
                </View>
                {members.some((client) => client.checked) && (
                  <View style={styles.selectAllContainer}>
                    <TouchableOpacity
                      style={{ flexDirection: "row", alignItems: "center" }}
                      onPress={toggleSelectAll}
                    >
                      <View
                        style={[
                          styles.checkbox,
                          {
                            backgroundColor: selectAll
                              ? colors.primaryBase
                              : "transparent",
                            borderWidth: 1,
                            borderColor: selectAll
                              ? "transparent"
                              : colors.border,
                          },
                        ]}
                      >
                        {selectAll && (
                          <View style={styles.checkboxInner}>
                            <CustomIcon
                              Icon={ICONS.CheckRightIcon}
                              height={12}
                              width={12}
                            />
                          </View>
                        )}
                      </View>
                      <Text
                        style={{
                          fontSize: 12,
                          fontWeight: "400",
                          color: colors.greyText,
                        }}
                      >
                        {members.filter((client) => client.checked).length}{" "}
                        MEMBERS SELECTED
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.deletebtn}
                      onPress={() => setIsModalVisible(true)}
                    >
                      <CustomIcon
                        Icon={ICONS.DeleteIcon}
                        height={20}
                        width={20}
                      />
                      <Text style={styles.deleteText}>Delete</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </>
            )}
            data={filteredMembers}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <CustomIcon Icon={ICONS.NoClientIcon} width={108} height={108} />
            <Text style={styles.noServiceText}>No team members</Text>
            <Text style={styles.noServiceSubText}>
              Add team members to manage your business together
            </Text>
          </View>
        )}
      </View>

      {/* Delete Confirmation Modal */}
      <Modal transparent={true} visible={isModalVisible} animationType="fade">
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => setIsModalVisible(false)}
          style={styles.modalContainer}
        >
          <View
            onStartShouldSetResponder={() => true} // Capture touch events
            onResponderRelease={(e) => e.stopPropagation()} // Prevent propagation
            style={styles.modalContent}
          >
            <View
              style={{
                alignItems: "center",
                paddingVertical: verticalScale(10),
              }}
            >
              <CustomIcon
                Icon={ICONS.DeleteConfirmationModalIcon}
                height={96}
                width={96}
              />
            </View>
            <Text style={styles.modalTitle}>Delete Team Members</Text>
            <Text style={styles.modalSubtitle}>
              Are you sure you want to delete the selected team members?. This
              operation cannot be undone.
            </Text>
            <TouchableOpacity
              style={styles.confirmDeleteBtn}
              onPress={deleteTeamMembers}
            >
              <CustomIcon Icon={ICONS.WhiteDeleteIcon} width={20} height={20} />
              <Text style={styles.confirmDeleteText}>Yes, delete</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.cancelDeleteBtn}
              onPress={() => setIsModalVisible(false)}
            >
              <Text style={styles.cancelDeleteText}>No, cancel</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
};

export default TeamMembers;

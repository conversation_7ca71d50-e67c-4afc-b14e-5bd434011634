import { StyleSheet } from "react-native";
import {
  moderateScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useTheme } from "../../Utilities/ThemeContext";

export const useThemedStyles = () => {
  const { colors, isDarkMode } = useTheme();
  
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(20),
    },
    headerContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    headerLeftContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    headerRightContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
    },
    addText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.primaryBase,
    },
    inputContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderRadius: 8,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(10),
      marginVertical: verticalScale(10),
    },
    input: {
      flex: 1,
      fontSize: 14,
      fontWeight: "400",
      marginLeft: moderateScale(10),
      color: colors.text,
    },
    sortButton: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(5),
      borderRadius: 20,
      backgroundColor: isDarkMode ? colors.bglight : colors.bgsoft,
    },
    sortText: {
      fontSize: 12,
      fontWeight: "500",
      color: colors.text,
    },
    selectAllContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingVertical: verticalScale(10),
    },
    checkbox: {
      width: 16,
      height: 16,
      borderRadius: 4,
      marginRight: 8,
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "transparent",
    },
    checkboxInner: {},
    deletebtn: {
      flexDirection: "row",
      alignItems: "center",
      gap: moderateScale(5),
      backgroundColor: isDarkMode ? colors.bglight : colors.bgsoft,
      paddingHorizontal: moderateScale(10),
      paddingVertical: verticalScale(5),
      borderRadius: 20,
    },
    deleteText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.text,
    },
    memberItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: 10,
      marginBottom: 10,
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      padding: moderateScale(12),
      borderRadius: 10,
    },
    memberItemChecked: {
      backgroundColor: isDarkMode ? colors.bglight : "#E0E0E0",
    },
    profileImage: {
      width: 40,
      height: 40,
      marginRight: 10,
      borderRadius: 100,
    },
    memberInfo: {
      flex: 1,
    },
    memberName: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.text,
    },
    memberEmail: {
      fontSize: 12,
      fontWeight: "400",
      marginTop: verticalScale(5),
      color: colors.greyText,
    },
    emptyContainer: {
      alignItems: "center",
      justifyContent: "center",
      flex: 1,
    },
    noServiceText: {
      fontSize: 16,
      fontWeight: "500",
      marginTop: verticalScale(10),
      color: colors.text,
    },
    noServiceSubText: {
      fontSize: 14,
      fontWeight: "400",
      textAlign: "center",
      color: colors.greyText,
    },
    modalContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.5)",
    },
    modalContent: {
      width: "90%",
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      borderRadius: 10,
      padding: 20,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
    },
    modalTitle: {
      fontSize: 24,
      fontWeight: "500",
      color: colors.text,
      textAlign: "center",
    },
    modalSubtitle: {
      fontSize: 16,
      fontWeight: "400",
      color: colors.text,
      textAlign: "center",
      marginVertical: 2,
    },
    confirmDeleteBtn: {
      backgroundColor: colors.stateerrorbase,
      padding: 10,
      borderRadius: 8,
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center",
      marginVertical: verticalScale(20),
    },
    confirmDeleteText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.white,
    },
    cancelDeleteBtn: {
      backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
      alignItems: "center",
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      padding: 10,
    },
    cancelDeleteText: {
      fontSize: 14,
      fontWeight: "400",
      color: colors.text,
    },
  });
};

import React, { useState } from "react";
import { FlatList, Text, TouchableOpacity, View } from "react-native";
import { CountryCode } from "react-native-country-picker-modal";
import { Asset } from "react-native-image-picker";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import ThemedBackButton from "../../Components/ThemedBackButton";
import { SetupStepsProps } from "../../Navigation/Typings";
import { postData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import { useTheme } from "../../Utilities/ThemeContext";
import Step1 from "./StepsScreens/Step1";
import Step2 from "./StepsScreens/Step2";
import Step3 from "./StepsScreens/Step3";
import { useThemedStyles } from "./themedStyles";

const SetupSteps = ({ navigation, route }: SetupStepsProps) => {
  const { isJoinExisting } = route.params;
  const [activeStep, setActiveStep] = useState(isJoinExisting ? 1 : 0);

  // Get theme context
  const { colors, isDarkMode } = useTheme();
  const styles = useThemedStyles();

  const [businessName, setBusinessName] = useState("next Salon");
  const [businessEmail, setBusinessEmail] = useState("<EMAIL>");
  const [description, setDescription] = useState("Affordable and The best");
  const [phoneNumber, setPhoneNumber] = useState("456685214");
  const [countryCode, setCountryCode] = useState<CountryCode>("IN");
  const [callingCode, setCallingCode] = useState("91");
  const [websiteUrl, setWebsiteUrl] = useState("testing URl");
  const [busineessLogo, setBusinessLogo] = useState<Asset | null>(null);

  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  const steps = isJoinExisting
    ? ["services", "Businesses"]
    : ["Details", "Services", "Finalise"];

  const goToNextStep = () => {
    if (activeStep === 0) {
      setActiveStep((prevStep) => prevStep + 1);
    } else if (activeStep === 1) {
      if (!isJoinExisting) {
        handleCreateBusineesProfile();
      }
    }
  };

  const handleCreateBusineesProfile = async () => {
    try {
      const body = {
        businessName,
        email: businessEmail,
        businessDescription: description,
        phoneNumber,
        countryCode: `+${callingCode}`,
        countryCallingCode: countryCode,
        websiteUrl,
        // businessLogo: busineessLogo,
        selectedCategories: selectedItems,
      };

      const response = await postData<any>(ENDPOINTS.createBusiness, body);
      if (response.data.success) {
        setActiveStep((prevStep) => prevStep + 1);
        Toast.show({
          type: "success",
          text1: response.data.message,
        });
      }
    } catch (error: any) {
      console.error("Error during business creation:", error);
      Toast.show({
        type: "error",
        text1: "Something went wrong. Please try again later.",
      });
    }
  };

  return (
    <View style={styles.mainContainer}>
      <SafeAreaView style={{ flex: 1 }}>
        <View style={styles.container}>
          {/* Step Indicator */}
          <View style={styles.stepIndicatorContainer}>
            {steps.map((step, index) => (
              <TouchableOpacity
                key={index}
                // onPress={() => setActiveStep(index)}
                style={styles.stepWrapper}
              >
                <View
                  style={[
                    styles.underline,
                    {
                      backgroundColor: isJoinExisting
                        ? index <= activeStep - 1
                          ? isDarkMode
                            ? colors.primaryBase
                            : colors.stateerrorbase
                          : colors.greyText
                        : index <= activeStep
                        ? isDarkMode
                          ? colors.primaryBase
                          : colors.stateerrorbase
                        : colors.greyText,
                    },
                  ]}
                />
                <Text
                  style={[
                    styles.stepText,
                    {
                      color: isJoinExisting
                        ? index <= activeStep - 1
                          ? isDarkMode
                            ? colors.primaryBase
                            : colors.stateerrorbase
                          : colors.greyText
                        : index <= activeStep
                        ? isDarkMode
                          ? colors.primaryBase
                          : colors.stateerrorbase
                        : colors.greyText,
                    },
                  ]}
                >
                  {step}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <TouchableOpacity
            style={styles.content}
            activeOpacity={0.8}
            onPress={() => {
              if (isJoinExisting) {
                if (activeStep === 1) {
                  navigation.goBack();
                } else {
                  setActiveStep((prevStep) => prevStep - 1);
                }
              } else {
                if (activeStep === 0) {
                  navigation.goBack();
                } else {
                  setActiveStep((prevStep) => prevStep - 1);
                }
              }
            }}
          >
            <ThemedBackButton onPress={() => {}} />
            {activeStep === 0 && (
              <Text style={styles.titleText}>Setup your business</Text>
            )}
            {activeStep === 1 && (
              <Text style={styles.titleText}>
                Select the services your business offers.
              </Text>
            )}
            {activeStep === 2 && (
              <Text style={styles.titleText}>
                {isJoinExisting ? "Businesses List" : "Finalise"}
              </Text>
            )}
          </TouchableOpacity>

          <View style={{ marginTop: 10, flex: 1 }}>
            {activeStep === 0 && (
              <Step1
                onContinue={goToNextStep}
                businessName={businessName}
                setBusinessName={setBusinessName}
                businessEmail={businessEmail}
                setBusinessEmail={setBusinessEmail}
                description={description}
                setDescription={setDescription}
                phoneNumber={phoneNumber}
                setPhoneNumber={setPhoneNumber}
                countryCode={countryCode}
                setCountryCode={setCountryCode}
                callingCode={callingCode}
                setCallingCode={setCallingCode}
                websiteUrl={websiteUrl}
                setWebsiteUrl={setWebsiteUrl}
                busineessLogo={busineessLogo}
                setBusinessLogo={setBusinessLogo}
              />
            )}
            {activeStep === 1 && (
              <Step2
                onContinue={goToNextStep}
                selectedItems={selectedItems}
                setSelectedItems={setSelectedItems}
              />
            )}
            {activeStep === 2 &&
              (isJoinExisting ? (
                <>
                  <FlatList
                    data={[]}
                    renderItem={() => <></>}
                    ListEmptyComponent={() => (
                      <Text style={styles.emptyText}>
                        No Businesses available currently.
                      </Text>
                    )}
                  />
                </>
              ) : (
                <Step3
                  onPressHome={() => {
                    navigation.replace("mainStack", {
                      screen: "bottomTabs",
                      params: { screen: "overView" },
                    });
                  }}
                />
              ))}
          </View>
        </View>
      </SafeAreaView>
    </View>
  );
};

export default SetupSteps;

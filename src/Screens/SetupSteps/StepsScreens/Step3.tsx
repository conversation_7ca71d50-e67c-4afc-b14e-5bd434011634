import React, { useState } from "react";
import { Image, Text, TouchableOpacity, View } from "react-native";
import { useDispatch } from "react-redux";
import { setSetupComplete } from "../../../Redux/slices/businessSlice";
import { setIsbusineesSetup } from "../../../Redux/slices/initialSlice";
import ImagePath from "../../../Utilities/Constants/ImagePath";
import { useThemedCommonStyles } from "../../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../../Utilities/ThemeContext";
import { useThemedStep3Styles } from "./themedStyles";
import useTranslation from "../../../Localization/useTranslation";

import Clipboard from "@react-native-clipboard/clipboard";

const ContractAndAgreements = ({ onPressHome }: any) => {
  const dispatch = useDispatch();
  const [isCopied, setIsCopied] = useState(false);
  const inviteLink = "glamup.com/invite?2a43fadx3"; // This might be a contract link in the future

  // Get theme context
  const { colors } = useTheme();
  const styles = useThemedStep3Styles();
  const themedCommonStyles = useThemedCommonStyles();
  const { business } = useTranslation();

  const copyToClipboard = () => {
    Clipboard.setString(inviteLink);
    setIsCopied(true);

    // Revert icon back to CopyIcon after 2 seconds
    setTimeout(() => setIsCopied(false), 10000);
  };

  return (
    <View style={{ flex: 1 }}>
      <View style={styles.container}>
        <Image
          source={ImagePath.ScheduleImage}
          resizeMode="contain"
          style={styles.image}
        />
        <View
          style={{
            paddingHorizontal: 20,
            paddingBottom: 20,
            flex: 1,
          }}
        >
          <Text style={styles.titleText}>
            {business("setupSteps.welcomeToGlamUP")}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.loginButton}
          activeOpacity={0.8}
          onPress={() => {
            // Mark business setup as complete in Redux
            dispatch(setSetupComplete(true));
            dispatch(setIsbusineesSetup(true));
            onPressHome();
          }}
        >
          <Text style={styles.loginText}>
            {business("setupSteps.goToHome")}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ContractAndAgreements;

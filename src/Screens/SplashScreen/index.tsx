import React, { useEffect } from "react";
import { Image, View } from "react-native";
import Toast from "react-native-toast-message";
import { SplashScreenProps } from "../../Navigation/Typings";
import { setIsAuth, setIsbusineesSetup } from "../../Redux/slices/initialSlice";
import { useAppDispatch } from "../../Redux/store";
import { GetProfileApiResponse } from "../../Services/ApiResponse";
import { fetchData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import ImagePath from "../../Utilities/Constants/ImagePath";
import {
  deleteLocalStorageData,
  getLocalStorageData,
} from "../../Utilities/Helpers";
import STORAGE_KEYS from "../../Utilities/StorageKeys";
import { Colors } from "../../Utilities/Styles/colors";
import { height, width } from "../../Utilities/Styles/responsiveSize";
import { setUser } from "../../Redux/slices/userSlice";

const SplashScreen = ({ navigation }: SplashScreenProps) => {
  const dispatch = useAppDispatch();

  const getUserData = async () => {
    try {
      const response = await fetchData<GetProfileApiResponse>(
        ENDPOINTS.getUserProfileData
      );

      if (response.data.success) {
        return response.data;
      }
    } catch (error: any) {
      console.error("Error during password reset:", error);
      if (!error.success) {
        await deleteLocalStorageData(STORAGE_KEYS.token);
        Toast.show({
          type: "error",
          text1: error.message || "Something went wrong. Please try again.",
        });
      }
    }
    return null;
  };

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Check if we have saved credentials
        const token = await getLocalStorageData(STORAGE_KEYS.token);

        // Wait for a minimum time to show splash screen
        await new Promise((resolve) => setTimeout(resolve, 1500));

        if (token) {
          const userData = await getUserData();
          if (userData) {
            dispatch(setUser(userData.data.profile));
            dispatch(setIsAuth(true));

            if (userData.data.profile.businessRole.length > 0) {
              dispatch(setIsbusineesSetup(true));
              navigation.replace("mainStack", {
                screen: "bottomTabs",
                params: { screen: "overView" },
              });
            } else {
              dispatch(setIsbusineesSetup(false));
              navigation.replace("setupStack", { screen: "SetupBusiness" });
            }
          }
        } else {
          // No saved credentials, go to login screen
          navigation.replace("authStack", { screen: "LoginScreen" });
        }
      } catch (error) {
        console.error("Error during app initialization:", error);
        navigation.replace("authStack", { screen: "LoginScreen" });
      }
    };

    initializeApp();
  }, []);

  return (
    <View style={{ flex: 1, backgroundColor: Colors.black }}>
      <Image
        source={ImagePath.Applogo}
        style={{ height: height, width: width }}
      />
    </View>
  );
};

export default SplashScreen;

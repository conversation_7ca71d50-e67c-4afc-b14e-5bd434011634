import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Image,
  Modal,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import {
  CallingCode,
  Country,
  CountryCode,
} from "react-native-country-picker-modal";
import DatePicker from "react-native-date-picker";
import {
  Asset,
  launchCamera,
  launchImageLibrary,
} from "react-native-image-picker";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icon";
import CustomIcon from "../../Components/CustomIcon";
import ThemedBackButton from "../../Components/ThemedBackButton";
import { setNewMembers, updateMember } from "../../Redux/slices/memberSlice";
import { useAppDispatch } from "../../Redux/store";
import { ThemedPhonePicker } from "../../Utilities/Components/ThemedHelpers";
import UploadImageOptions from "../../Utilities/Components/Modal/UploadImageOptions";
import { showErrorToast } from "../../Utilities/toastUtils";
import {
  moderateScale,
  moderateScaleVertical,
  textScale,
  verticalScale,
} from "../../Utilities/Styles/responsiveSize";
import { useThemedCommonStyles } from "../../Utilities/Styles/themedCommonStyles";
import { useTheme } from "../../Utilities/ThemeContext";
import { useThemedStyles } from "./themedStyles";
import { AddTeamMemberProps } from "../../Navigation/Typings";
import { postData, putData } from "../../Services/ApiService";
import ENDPOINTS from "../../Services/EndPoints";
import Toast from "react-native-toast-message";
import { formatDateToYYYYMMDD } from "../../Utilities/Helpers";
import { AddNewTeamMemberApiResponse } from "../../Services/ApiResponse";
import useTranslation from "../../Localization/useTranslation";

const AddTeamMember = ({ navigation, route }: AddTeamMemberProps) => {
  const dispatch = useAppDispatch();
  const { memberData } = route.params;
  const { colors, isDarkMode } = useTheme();
  const styles = useThemedStyles();
  const themedCommonStyles = useThemedCommonStyles();
  const { team, common } = useTranslation();

  const [businessName, setBusinessName] = useState(memberData?.name || "");
  const [Email, setEmail] = useState(memberData?.email || "");
  const [phoneNumber, setPhoneNumber] = useState(memberData?.number || "");

  const [countryVisible, setCountryVisible] = useState(false);
  const [countryCode, setCountryCode] = useState<CountryCode>(
    memberData?.counryCode || "IN"
  );
  const [callingCode, setCallingCode] = useState<CallingCode>(
    memberData?.callingCode
  );

  const [isButtonEnabled, setIsButtonEnabled] = useState(false);
  const [date, setDate] = useState<any>(() => {
    // Handle both dob and birthday properties, and convert string to Date object
    const birthdateString = memberData?.dob || memberData?.birthday;
    if (birthdateString) {
      // Try to parse the date string to a Date object
      const parsedDate = new Date(birthdateString);
      // Check if the parsed date is valid
      return isNaN(parsedDate.getTime()) ? null : parsedDate;
    }
    return null;
  });
  const [open, setOpen] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedGender, setSelectedGender] = useState<string | null>(
    memberData?.gender || null
  );

  const [image, setImage] = useState<any>(memberData?.image || null);
  const [isImageModalVisible, setIsImageModalVisible] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const onClickCapture = () => {
    setIsImageModalVisible(true);
  };

  const handleImagePick = () => {
    launchImageLibrary({ mediaType: "photo", quality: 0.8 }, (response) => {
      if (response.didCancel) {
        console.log("User cancelled image picker");
      } else if (response.errorCode) {
        console.log("ImagePicker Error: ", response.errorMessage);
      } else if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        setImage(asset.uri);
      } else {
        console.log("No image selected or unexpected response:", response);
      }
      setIsImageModalVisible(false);
    });
  };

  const handleCameraPick = async () => {
    try {
      const result = await launchCamera({
        quality: 1,
        mediaType: "photo",
      });

      if (result.didCancel) {
        console.log("User cancelled camera");
      } else if (result.errorCode) {
        console.log("Camera error:", result.errorMessage);
      } else if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        setImage(asset.uri);
      } else {
        console.log("No image captured or unexpected response:", result);
      }
      setIsImageModalVisible(false);
    } catch (error) {
      console.log("Camera capture failed:", error);
      showErrorToast("Camera Error", "Failed to capture photo");
    }
  };

  const onSelect = (country: Country) => {
    setCountryCode(country.cca2);
    setCallingCode(country.callingCode?.[0] ?? ""); // Ensure calling code updates
    setCountryVisible(false); // Close the picker after selection
  };

  const openPicker = () => {
    setCountryVisible(!countryVisible);
  };

  const handleGenderSelect = (gender: string) => {
    setSelectedGender(gender);
    setModalVisible(false);
  };

  useEffect(() => {
    setIsButtonEnabled(
      businessName.trim() !== "" &&
        Email.trim() !== "" &&
        phoneNumber.trim() !== ""
    );
  }, [businessName, Email, phoneNumber]);

  const handleAddMember = async () => {
    setIsLoading(true);
    if (memberData) {
      try {
        const response = await putData<AddNewTeamMemberApiResponse>(
          `${ENDPOINTS.createTeamMember}/${memberData.id}`,
          {
            name: businessName,
            email: Email,
            phoneNumber: phoneNumber,
            countryCode: `+${callingCode}`,
            countryCallingCode: countryCode,
            gender: selectedGender?.toLowerCase(),
            birthday: formatDateToYYYYMMDD(date),
            profilePicture:
              "https://images.unsplash.com/photo-1485965120184-e220f721d03e?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
          }
        );

        if (response.data.success) {
          dispatch(
            updateMember({
              id: response.data.data.teamMember._id,
              name: response.data.data.teamMember.name,
              email: response.data.data.teamMember.email,
              number: response.data.data.teamMember.phoneNumber,
              image: response.data.data.teamMember.profilePicture,
              checked: false,
              callingCode: response.data.data.teamMember.countryCode,
              countryCode: response.data.data.teamMember.countryCallingCode,
              gender: response.data.data.teamMember.gender,
              birthday: response.data.data.teamMember.birthday, // Keep as birthday for consistency
              dob: response.data.data.teamMember.birthday, // Also add as dob for backward compatibility
            })
          );
          Toast.show({
            type: "success",
            text1: response.data.message,
          });
        }
      } catch (error: any) {
        console.error("Error updating team member:", error);
        showErrorToast(
          "Error",
          error.message || "Something went wrong. Please try again."
        );
      } finally {
        setIsLoading(false);
      }
    } else {
      try {
        const response = await postData<AddNewTeamMemberApiResponse>(
          ENDPOINTS.createTeamMember,
          {
            name: businessName,
            email: Email,
            phoneNumber: phoneNumber,
            countryCode: `+${callingCode}`,
            countryCallingCode: countryCode,
            gender: selectedGender?.toLowerCase(),
            birthday: formatDateToYYYYMMDD(date),
            profilePicture:
              "https://images.unsplash.com/photo-1485965120184-e220f721d03e?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
          }
        );

        if (response.data.success) {
          dispatch(
            setNewMembers({
              id: response.data.data.teamMember._id,
              name: response.data.data.teamMember.name,
              email: response.data.data.teamMember.email,
              number: response.data.data.teamMember.phoneNumber,
              image: response.data.data.teamMember.profilePicture,
              checked: false,
              callingCode: response.data.data.teamMember.countryCode,
              countryCode: response.data.data.teamMember.countryCallingCode,
              gender: response.data.data.teamMember.gender,
              birthday: response.data.data.teamMember.birthday, // Keep as birthday for consistency
              dob: response.data.data.teamMember.birthday, // Also add as dob for backward compatibility
            })
          );

          Toast.show({
            type: "success",
            text1: response.data.message,
          });
          navigation.goBack();
        }
      } catch (error: any) {
        console.error("Error creating team member:", error);
        showErrorToast(
          "Error",
          error.message || "Something went wrong. Please try again."
        );
      } finally {
        setIsLoading(false);
      }
    }
  };

  console.log(memberData);

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView
        bounces={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ gap: verticalScale(20) }}
      >
        {/* Header */}
        <View style={styles.headerContainer}>
          <ThemedBackButton
            onPress={() => {
              navigation.goBack();
            }}
            style={{
              paddingVertical: verticalScale(5),
              paddingLeft: moderateScale(5),
              paddingRight: moderateScale(10),
            }}
            width={13}
            height={13}
          />
          <Text style={themedCommonStyles.font24W500}>
            {memberData
              ? team("editTeamMemberDetails")
              : team("addNewTeamMember")}
          </Text>
        </View>

        <View style={{ paddingHorizontal: verticalScale(10) }}>
          <View
            style={{
              marginTop: verticalScale(30),
              marginBottom: verticalScale(10),
            }}
          >
            <Text style={styles.sectionTitle}>{team("memberDetails")}</Text>
            <Text style={styles.sectionSubtitle}>
              {team("manageTeamMembers")}
            </Text>
          </View>

          <TouchableOpacity
            onPress={onClickCapture}
            style={styles.detailiconContainer}
          >
            {image ? (
              <Image
                source={{ uri: image }}
                style={{ height: 90, width: 90, borderRadius: 100 }}
              />
            ) : (
              <CustomIcon Icon={ICONS.DetailsStepIcon} height={30} width={30} />
            )}
            <View style={{ position: "absolute", bottom: -5, right: -5 }}>
              <CustomIcon
                Icon={image ? ICONS.EditServices : ICONS.CameraIcon}
                height={40}
                width={40}
              />
            </View>
          </TouchableOpacity>
          {/* Name */}
          <Text style={styles.labelText}>
            {team("name")} <Text style={styles.requiredText}>*</Text>
          </Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="John Doe"
              placeholderTextColor={colors.greyText}
              value={businessName}
              onChangeText={setBusinessName}
              keyboardType="default"
            />
          </View>
          {/* Email */}
          <Text style={styles.labelText}>
            {team("email")} <Text style={styles.requiredText}>*</Text>
          </Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="<EMAIL>"
              placeholderTextColor={colors.greyText}
              value={Email}
              onChangeText={setEmail}
              keyboardType="default"
            />
          </View>
          {/* Phone Number */}
          <Text style={styles.labelText}>{team("phoneNumber")}</Text>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginTop: verticalScale(10),
            }}
          >
            <View style={styles.phoneview}>
              <ThemedPhonePicker
                visible={countryVisible}
                countryCode={countryCode}
                onSelect={onSelect}
                onPress={openPicker}
              />
            </View>
            <View style={styles.phoneCountry}>
              <TextInput
                style={[styles.input, { flex: 1 }]}
                placeholder="00 00 0000"
                placeholderTextColor={colors.greyText}
                value={phoneNumber}
                onChangeText={setPhoneNumber}
                keyboardType="number-pad"
              />
            </View>
          </View>

          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              gap: moderateScale(20),
            }}
          >
            <View style={{ marginTop: moderateScaleVertical(20), flex: 1 }}>
              <Text style={styles.labelText}>{team("birthday")}</Text>
              <TouchableOpacity
                style={styles.dategender}
                activeOpacity={0.8}
                onPress={() => setOpen(true)}
              >
                <CustomIcon Icon={ICONS.Calender} width={20} height={20} />
                <Text
                  style={[styles.dateText, !date && styles.placeholderText]}
                >
                  {date ? date.toLocaleDateString() : "dd / mm / yyyy"}
                </Text>
              </TouchableOpacity>

              <DatePicker
                modal
                open={open}
                date={date ?? new Date()}
                mode="date"
                maximumDate={new Date()}
                onConfirm={(selectedDate) => {
                  setOpen(false);
                  setDate(selectedDate);
                }}
                onCancel={() => setOpen(false)}
                theme={isDarkMode ? "dark" : "light"}
              />
            </View>
            <View
              style={{
                marginTop: 20,
                flex: 1,
              }}
            >
              <Text style={styles.labelText}>{team("gender")}</Text>
              <TouchableOpacity
                style={styles.dategender}
                activeOpacity={0.8}
                onPress={() => setModalVisible(true)}
              >
                <Text
                  style={{
                    fontSize: textScale(14),
                    color: selectedGender ? colors.text : colors.greyText,
                  }}
                >
                  {selectedGender ? selectedGender : team("selectGender")}
                </Text>
              </TouchableOpacity>

              {/* Modal */}
              <Modal
                transparent={true}
                visible={modalVisible}
                animationType="slide"
              >
                <View style={styles.modalContainer}>
                  <View style={styles.modalContent}>
                    <Text style={styles.modalTitle}>
                      {team("selectGender")}
                    </Text>

                    <TouchableOpacity
                      style={styles.option}
                      onPress={() => handleGenderSelect("Male")}
                    >
                      <Text style={styles.optionText}>{team("male")}</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={styles.option}
                      onPress={() => handleGenderSelect("Female")}
                    >
                      <Text style={styles.optionText}>{team("female")}</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={styles.option}
                      onPress={() => handleGenderSelect("Other")}
                    >
                      <Text style={styles.optionText}>{team("other")}</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={styles.cancelButton}
                      onPress={() => setModalVisible(false)}
                    >
                      <Text style={styles.cancelText}>{common("cancel")}</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </Modal>
            </View>
          </View>

          <TouchableOpacity
            style={[
              styles.loginButton,
              {
                backgroundColor: isButtonEnabled
                  ? isDarkMode
                    ? colors.primaryBase
                    : colors.maintext
                  : colors.bgsoft,
              },
            ]}
            disabled={!isButtonEnabled}
            activeOpacity={0.8}
            onPress={handleAddMember}
          >
            {isLoading ? (
              <ActivityIndicator />
            ) : (
              <Text
                style={[
                  styles.loginText,
                  {
                    color: isButtonEnabled ? colors.white : colors.greyText,
                  },
                ]}
              >
                {memberData ? team("updateTeamMember") : team("saveTeamMember")}
              </Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Image Upload Options Modal */}
        <UploadImageOptions
          isModalVisible={isImageModalVisible}
          closeModal={() => setIsImageModalVisible(false)}
          onPressCamera={handleCameraPick}
          onPressGallery={handleImagePick}
        />
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default AddTeamMember;

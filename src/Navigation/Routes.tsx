import { NavigationContainer } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import React from "react";
import "react-native-gesture-handler";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import BottomTabBar from "../Components/BottomTabBar";
import SplashScreen from "../Screens/SplashScreen";
import * as screens from "./index";
import {
  AuthStackParams,
  BottomTabStackParams,
  MainStackParams,
  RootStackParams,
  SetUpStackParams,
} from "./Typings";

const Stack = createNativeStackNavigator<RootStackParams>();
const Auth = createNativeStackNavigator<AuthStackParams>();
const Setup = createNativeStackNavigator<SetUpStackParams>();
const Main = createNativeStackNavigator<MainStackParams>();

const BottomTab = createBottomTabNavigator<BottomTabStackParams>();

export default function Routes() {
  function AuthStack() {
    return (
      <Auth.Navigator
        screenOptions={{
          headerShown: false,
        }}
      >
        <Auth.Screen
          name="LoginScreen"
          component={screens?.LoginScreen}
          options={{ headerShown: false }}
        />
        <Auth.Screen
          name="SignupScreen"
          component={screens?.SignupScreen}
          options={{ headerShown: false }}
        />
        <Auth.Screen
          name="PasswordReset"
          component={screens?.PasswordReset}
          options={{ headerShown: false }}
        />
        <Auth.Screen
          name="VerificationCode"
          component={screens?.VerificationCode}
          options={{ headerShown: false }}
        />
        <Auth.Screen
          name="SetNewPassword"
          component={screens?.SetNewPassword}
          options={{ headerShown: false }}
        />
      </Auth.Navigator>
    );
  }

  function MainStack() {
    return (
      <Main.Navigator
        screenOptions={{
          headerShown: false,
        }}
      >
        <Main.Screen
          name="bottomTabs"
          component={TabStack}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="settings"
          component={screens?.SettingsScreen}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="businessDetails"
          component={screens?.BusinessDetail}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="serviceList"
          component={screens?.ServicesList}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="addNewService"
          component={screens?.NewServices}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="addNewPackage"
          component={screens?.NewPackage}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="editPackage"
          component={screens?.EditPackage}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="manageCategories"
          component={screens?.ManageCategories}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="addNewCategory"
          component={screens?.NewCategory}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="manageTeam"
          component={screens?.ManageTeam}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="manageTeamMember"
          component={screens?.TeamMembers}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="addTeamMember"
          component={screens?.AddTeamMember}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="managePermissions"
          component={screens?.ManagePermissions}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="addPermissionRole"
          component={screens?.AddPermissionRole}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="manageClient"
          component={screens?.ManageClients}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="addClient"
          component={screens?.AddNewClient}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="TeamMeberDetail"
          component={screens?.TeamMeberDetail}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="editProfile"
          component={screens?.EditProfile}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="notifications"
          component={screens?.Notifications}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="termsOfService"
          component={screens?.TermsofService}
          options={{ headerShown: false }}
        />
        <Main.Screen
          name="privacyPolicy"
          component={screens?.PrivacyPolicy}
          options={{ headerShown: false }}
        />
      </Main.Navigator>
    );
  }

  function SetUpStack() {
    return (
      <Setup.Navigator
        screenOptions={{
          headerShown: false,
        }}
      >
        <Setup.Screen
          name="SetupBusiness"
          component={screens?.SetupCreateJoin}
          options={{ headerShown: false }}
        />
        <Setup.Screen
          name="setupSteps"
          component={screens?.SetupSteps}
          options={{ headerShown: false }}
        />
      </Setup.Navigator>
    );
  }

  function TabStack() {
    return (
      <BottomTab.Navigator
        screenOptions={{
          headerShown: false,
        }}
        tabBar={(props) => <BottomTabBar {...props} />}
      >
        {/* OverView Tab */}
        <BottomTab.Screen name="overView" component={screens.OverView} />
        {/* Manage Tab */}
        <BottomTab.Screen name="manage" component={screens.Manage} />
      </BottomTab.Navigator>
    );
  }

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{ animation: "ios_from_right", headerShown: false }}
      >
        <Stack.Screen name="splash" component={SplashScreen} />
        <Stack.Screen name="authStack" component={AuthStack} />
        <Stack.Screen name="setupStack" component={SetUpStack} />
        <Stack.Screen name="mainStack" component={MainStack} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

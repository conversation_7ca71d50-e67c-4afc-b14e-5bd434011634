import { useEffect, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../Redux/store';
import { fetchData } from '../Services/ApiService';
import ENDPOINTS from '../Services/EndPoints';
import Toast from 'react-native-toast-message';

// Import all slice actions
import { setClients } from '../Redux/slices/clientSlice';
import { setMembers } from '../Redux/slices/memberSlice';
import { setCategories } from '../Redux/slices/categorySlice';
import { setCategoriesWithServices, setLoading as setServicesLoading } from '../Redux/slices/servicesSlice';
import { setPackages, setLoading as setPackagesLoading } from '../Redux/slices/packagesSlice';

// Import API response types
import {
  GetAllClientsApiResponse,
  GetAllTeamMembersApiResponse,
  GetAllUserCategoriesApiResponse,
  GetAllServicesApiResponse,
  GetAllPackagesApiResponse,
} from '../Services/ApiResponse';

// Data refresh timestamps to prevent unnecessary API calls
interface DataTimestamps {
  clients: number;
  members: number;
  categories: number;
  services: number;
  packages: number;
}

// Cache duration in milliseconds (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;

// Global timestamps store
let dataTimestamps: DataTimestamps = {
  clients: 0,
  members: 0,
  categories: 0,
  services: 0,
  packages: 0,
};

export const useDataManager = () => {
  const dispatch = useAppDispatch();
  
  // Get current data from Redux store
  const clients = useAppSelector((state) => state.clients.clients);
  const members = useAppSelector((state) => state.members.members);
  const categories = useAppSelector((state) => state.categories.categories);
  const services = useAppSelector((state) => state.services.categoriesWithServices);
  const packages = useAppSelector((state) => state.packages.packages);
  
  // Loading states
  const servicesLoading = useAppSelector((state) => state.services.isLoading);
  const packagesLoading = useAppSelector((state) => state.packages.isLoading);

  // Check if data needs refresh based on cache duration
  const needsRefresh = useCallback((dataType: keyof DataTimestamps): boolean => {
    const now = Date.now();
    const lastFetch = dataTimestamps[dataType];
    return now - lastFetch > CACHE_DURATION;
  }, []);

  // Update timestamp when data is fetched
  const updateTimestamp = useCallback((dataType: keyof DataTimestamps) => {
    dataTimestamps[dataType] = Date.now();
  }, []);

  // Fetch clients data
  const fetchClients = useCallback(async (force: boolean = false) => {
    if (!force && clients.length > 0 && !needsRefresh('clients')) {
      return clients;
    }

    try {
      const response = await fetchData<GetAllClientsApiResponse>(
        ENDPOINTS.getAllClients
      );
      
      if (response.data.success) {
        const formattedClients = response.data.data.clients.map((client) => ({
          id: client._id,
          name: client.name,
          email: client.email,
          number: client.phoneNumber,
          countryCode: client.countryCode,
          countryCallingCode: client.countryCallingCode,
          image: client.profilePicture,
          birthday: client.birthday,
          gender: client.gender,
          checked: false,
        }));
        
        dispatch(setClients(formattedClients));
        updateTimestamp('clients');
        return formattedClients;
      }
    } catch (error: any) {
      console.error('Error fetching clients:', error);
      Toast.show({
        type: 'error',
        text1: 'Failed to fetch clients',
        text2: error.message || 'Please try again later.',
      });
    }
    return clients;
  }, [dispatch, clients, needsRefresh, updateTimestamp]);

  // Fetch team members data
  const fetchMembers = useCallback(async (force: boolean = false) => {
    if (!force && members.length > 0 && !needsRefresh('members')) {
      return members;
    }

    try {
      const response = await fetchData<GetAllTeamMembersApiResponse>(
        ENDPOINTS.getAllTeamMembers
      );
      
      if (response.data.success) {
        const formattedMembers = response.data.data.teamMembers.map((member) => ({
          id: member._id,
          name: member.name,
          email: member.email,
          number: member.phoneNumber,
          countryCode: member.countryCode,
          callingCode: member.countryCallingCode,
          image: member.profilePicture,
          checked: false,
        }));
        
        dispatch(setMembers(formattedMembers));
        updateTimestamp('members');
        return formattedMembers;
      }
    } catch (error: any) {
      console.error('Error fetching team members:', error);
      Toast.show({
        type: 'error',
        text1: 'Failed to fetch team members',
        text2: error.message || 'Please try again later.',
      });
    }
    return members;
  }, [dispatch, members, needsRefresh, updateTimestamp]);

  // Fetch categories data
  const fetchCategories = useCallback(async (force: boolean = false) => {
    if (!force && categories.length > 0 && !needsRefresh('categories')) {
      return categories;
    }

    try {
      const response = await fetchData<GetAllUserCategoriesApiResponse>(
        ENDPOINTS.getAlluserCategories
      );
      
      if (response.data.success) {
        const formattedCategories = response.data.data.categories.map((cat) => ({
          id: cat._id,
          name: cat.name,
          description: cat.description,
        }));
        
        dispatch(setCategories(formattedCategories));
        updateTimestamp('categories');
        return formattedCategories;
      }
    } catch (error: any) {
      console.error('Error fetching categories:', error);
      Toast.show({
        type: 'error',
        text1: 'Failed to fetch categories',
        text2: error.message || 'Please try again later.',
      });
    }
    return categories;
  }, [dispatch, categories, needsRefresh, updateTimestamp]);

  // Fetch services data
  const fetchServices = useCallback(async (force: boolean = false) => {
    if (!force && services.length > 0 && !needsRefresh('services')) {
      return services;
    }

    dispatch(setServicesLoading(true));
    try {
      const response = await fetchData<GetAllServicesApiResponse>(
        ENDPOINTS.getAllServices
      );
      
      if (response.data.success) {
        dispatch(setCategoriesWithServices(response.data.data.categoriesWithServices));
        updateTimestamp('services');
        return response.data.data.categoriesWithServices;
      }
    } catch (error: any) {
      console.error('Error fetching services:', error);
      Toast.show({
        type: 'error',
        text1: 'Failed to fetch services',
        text2: error.message || 'Please try again later.',
      });
    } finally {
      dispatch(setServicesLoading(false));
    }
    return services;
  }, [dispatch, services, needsRefresh, updateTimestamp]);

  // Fetch packages data
  const fetchPackages = useCallback(async (force: boolean = false) => {
    if (!force && packages.length > 0 && !needsRefresh('packages')) {
      return packages;
    }

    dispatch(setPackagesLoading(true));
    try {
      const response = await fetchData<GetAllPackagesApiResponse>(
        ENDPOINTS.getAllPackages
      );
      
      if (response.data.success) {
        dispatch(setPackages(response.data.data.packages));
        updateTimestamp('packages');
        return response.data.data.packages;
      }
    } catch (error: any) {
      console.error('Error fetching packages:', error);
      Toast.show({
        type: 'error',
        text1: 'Failed to fetch packages',
        text2: error.message || 'Please try again later.',
      });
    } finally {
      dispatch(setPackagesLoading(false));
    }
    return packages;
  }, [dispatch, packages, needsRefresh, updateTimestamp]);

  // Fetch all data at once
  const fetchAllData = useCallback(async (force: boolean = false) => {
    const promises = [
      fetchClients(force),
      fetchMembers(force),
      fetchCategories(force),
      fetchServices(force),
      fetchPackages(force),
    ];
    
    await Promise.allSettled(promises);
  }, [fetchClients, fetchMembers, fetchCategories, fetchServices, fetchPackages]);

  // Force refresh specific data type
  const refreshData = useCallback(async (dataType: keyof DataTimestamps) => {
    switch (dataType) {
      case 'clients':
        return await fetchClients(true);
      case 'members':
        return await fetchMembers(true);
      case 'categories':
        return await fetchCategories(true);
      case 'services':
        return await fetchServices(true);
      case 'packages':
        return await fetchPackages(true);
      default:
        return null;
    }
  }, [fetchClients, fetchMembers, fetchCategories, fetchServices, fetchPackages]);

  // Clear all data (useful for logout)
  const clearAllData = useCallback(() => {
    dispatch(setClients([]));
    dispatch(setMembers([]));
    dispatch(setCategories([]));
    dispatch(setCategoriesWithServices([]));
    dispatch(setPackages([]));
    
    // Reset timestamps
    dataTimestamps = {
      clients: 0,
      members: 0,
      categories: 0,
      services: 0,
      packages: 0,
    };
  }, [dispatch]);

  return {
    // Data
    clients,
    members,
    categories,
    services,
    packages,
    
    // Loading states
    servicesLoading,
    packagesLoading,
    
    // Fetch functions
    fetchClients,
    fetchMembers,
    fetchCategories,
    fetchServices,
    fetchPackages,
    fetchAllData,
    
    // Utility functions
    refreshData,
    clearAllData,
    needsRefresh,
  };
};

import { useEffect, useState } from 'react';
import { useDataManager } from './useDataManager';

// Hook for clients data
export const useClients = (autoFetch: boolean = true) => {
  const { clients, fetchClients, refreshData } = useDataManager();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (autoFetch && clients.length === 0) {
      setIsLoading(true);
      fetchClients().finally(() => setIsLoading(false));
    }
  }, [autoFetch, clients.length, fetchClients]);

  const refresh = async () => {
    setIsLoading(true);
    await refreshData('clients');
    setIsLoading(false);
  };

  return {
    clients,
    isLoading,
    refresh,
    isEmpty: clients.length === 0,
  };
};

// Hook for team members data
export const useTeamMembers = (autoFetch: boolean = true) => {
  const { members, fetchMembers, refreshData } = useDataManager();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (autoFetch && members.length === 0) {
      setIsLoading(true);
      fetchMembers().finally(() => setIsLoading(false));
    }
  }, [autoFetch, members.length, fetchMembers]);

  const refresh = async () => {
    setIsLoading(true);
    await refreshData('members');
    setIsLoading(false);
  };

  return {
    members,
    isLoading,
    refresh,
    isEmpty: members.length === 0,
  };
};

// Hook for categories data
export const useCategories = (autoFetch: boolean = true) => {
  const { categories, fetchCategories, refreshData } = useDataManager();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (autoFetch && categories.length === 0) {
      setIsLoading(true);
      fetchCategories().finally(() => setIsLoading(false));
    }
  }, [autoFetch, categories.length, fetchCategories]);

  const refresh = async () => {
    setIsLoading(true);
    await refreshData('categories');
    setIsLoading(false);
  };

  return {
    categories,
    isLoading,
    refresh,
    isEmpty: categories.length === 0,
  };
};

// Hook for services data
export const useServices = (autoFetch: boolean = true) => {
  const { services, servicesLoading, fetchServices, refreshData } = useDataManager();

  useEffect(() => {
    if (autoFetch && services.length === 0) {
      fetchServices();
    }
  }, [autoFetch, services.length, fetchServices]);

  const refresh = async () => {
    await refreshData('services');
  };

  return {
    services,
    isLoading: servicesLoading,
    refresh,
    isEmpty: services.length === 0,
  };
};

// Hook for packages data
export const usePackages = (autoFetch: boolean = true) => {
  const { packages, packagesLoading, fetchPackages, refreshData } = useDataManager();

  useEffect(() => {
    if (autoFetch && packages.length === 0) {
      fetchPackages();
    }
  }, [autoFetch, packages.length, fetchPackages]);

  const refresh = async () => {
    await refreshData('packages');
  };

  return {
    packages,
    isLoading: packagesLoading,
    refresh,
    isEmpty: packages.length === 0,
  };
};

// Hook for all data with loading states
export const useAllAppData = (autoFetch: boolean = true) => {
  const dataManager = useDataManager();
  const [isInitialLoading, setIsInitialLoading] = useState(false);

  useEffect(() => {
    if (autoFetch) {
      const hasAnyData = 
        dataManager.clients.length > 0 ||
        dataManager.members.length > 0 ||
        dataManager.categories.length > 0 ||
        dataManager.services.length > 0 ||
        dataManager.packages.length > 0;

      if (!hasAnyData) {
        setIsInitialLoading(true);
        dataManager.fetchAllData().finally(() => setIsInitialLoading(false));
      }
    }
  }, [autoFetch, dataManager]);

  const refreshAll = async () => {
    setIsInitialLoading(true);
    await dataManager.fetchAllData(true);
    setIsInitialLoading(false);
  };

  return {
    ...dataManager,
    isInitialLoading,
    refreshAll,
    hasAnyData: 
      dataManager.clients.length > 0 ||
      dataManager.members.length > 0 ||
      dataManager.categories.length > 0 ||
      dataManager.services.length > 0 ||
      dataManager.packages.length > 0,
  };
};

// Hook for filtered data based on search
export const useFilteredData = <T extends { name: string }>(
  data: T[],
  searchTerm: string
) => {
  const filteredData = data.filter((item) =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return {
    filteredData,
    hasResults: filteredData.length > 0,
    resultCount: filteredData.length,
  };
};

// Hook for sorted data
export const useSortedData = <T extends { name: string }>(
  data: T[],
  sortDirection: 'asc' | 'desc' = 'asc'
) => {
  const sortedData = [...data].sort((a, b) => {
    if (sortDirection === 'asc') {
      return a.name.localeCompare(b.name);
    } else {
      return b.name.localeCompare(a.name);
    }
  });

  return {
    sortedData,
  };
};

// Hook for paginated data
export const usePaginatedData = <T>(
  data: T[],
  itemsPerPage: number = 10
) => {
  const [currentPage, setCurrentPage] = useState(1);

  const totalPages = Math.ceil(data.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentData = data.slice(startIndex, endIndex);

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  return {
    currentData,
    currentPage,
    totalPages,
    hasNextPage: currentPage < totalPages,
    hasPrevPage: currentPage > 1,
    goToPage,
    nextPage,
    prevPage,
    totalItems: data.length,
  };
};

// Hook for data with search, sort, and pagination
export const useDataTable = <T extends { name: string }>(
  data: T[],
  initialSearchTerm: string = '',
  initialSortDirection: 'asc' | 'desc' = 'asc',
  itemsPerPage: number = 10
) => {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [sortDirection, setSortDirection] = useState(initialSortDirection);

  const { filteredData } = useFilteredData(data, searchTerm);
  const { sortedData } = useSortedData(filteredData, sortDirection);
  const paginationData = usePaginatedData(sortedData, itemsPerPage);

  const toggleSort = () => {
    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
  };

  return {
    ...paginationData,
    searchTerm,
    setSearchTerm,
    sortDirection,
    setSortDirection,
    toggleSort,
    filteredCount: filteredData.length,
    totalCount: data.length,
  };
};

import React, { useState } from "react";
import {
  ActivityIndicator,
  Modal,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import Toast from "react-native-toast-message";
import ICONS from "../Assets/Icon";
import { postData } from "../Services/ApiService";
import ENDPOINTS from "../Services/EndPoints";
import { deleteLocalStorageData } from "../Utilities/Helpers";
import STORAGE_KEYS from "../Utilities/StorageKeys";
import {
  moderateScale,
  verticalScale,
} from "../Utilities/Styles/responsiveSize";
import { useTheme } from "../Utilities/ThemeContext";
import CustomIcon from "./CustomIcon";

interface DeactivateAccountModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const DeactivateAccountModal: React.FC<DeactivateAccountModalProps> = ({
  visible,
  onClose,
  onSuccess,
}) => {
  const [password, setPassword] = useState("");
  const [secureText, setSecureText] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const { colors, isDarkMode } = useTheme();

  const handleConfirm = async () => {
    if (!password.trim()) {
      Toast.show({
        type: "error",
        text1: "Password is required",
        text2: "Please enter your password to confirm account deactivation",
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await postData(ENDPOINTS.deActivateAccount, {
        password: password,
      });

      if (response.data.success) {
        Toast.show({
          type: "success",
          text1: response.data.message,
        });

        // Clear local storage
        await deleteLocalStorageData(STORAGE_KEYS.token);
        await deleteLocalStorageData(STORAGE_KEYS.credentials);

        // Reset form
        setPassword("");
        setSecureText(true);

        // Call success callback
        onSuccess();
      }
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error.message || "Something went wrong",
        text2: "Please try again later",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setPassword("");
    setSecureText(true);
    onClose();
  };

  const modalStyles = {
    overlay: {
      flex: 1,
      backgroundColor: "rgba(0,0,0,0.5)",
      justifyContent: "center" as const,
      alignItems: "center" as const,
    },
    modalContainer: {},
    header: {
      alignItems: "center" as const,
      marginBottom: verticalScale(20),
    },
    iconContainer: {
      width: 64,
      height: 64,
      borderRadius: 32,
      backgroundColor: colors.stateerrorbase + "20",
      justifyContent: "center" as const,
      alignItems: "center" as const,
      marginBottom: verticalScale(16),
    },
    title: {
      fontSize: 20,
      fontWeight: "600" as const,
      color: colors.text,
      textAlign: "center" as const,
      marginBottom: verticalScale(8),
    },
    subtitle: {
      fontSize: 14,
      fontWeight: "400" as const,
      color: colors.greyText,
      textAlign: "center" as const,
      lineHeight: 20,
    },
    inputContainer: {
      marginBottom: verticalScale(24),
    },
    label: {
      fontSize: 14,
      fontWeight: "500" as const,
      color: colors.text,
      marginBottom: verticalScale(8),
    },
    passwordInputContainer: {
      flexDirection: "row" as const,
      alignItems: "center" as const,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      paddingHorizontal: moderateScale(16),
      paddingVertical: moderateScale(12),
      backgroundColor: isDarkMode ? colors.bglight : colors.white,
    },
    passwordInput: {
      flex: 1,
      fontSize: 16,
      color: colors.text,
      marginLeft: moderateScale(12),
    },
    eyeButton: {
      padding: moderateScale(4),
    },
    buttonContainer: {
      flexDirection: "row" as const,
      gap: moderateScale(12),
    },
    cancelButton: {
      flex: 1,
      paddingVertical: moderateScale(14),
      borderRadius: 12,
      borderWidth: 1,
      borderColor: colors.border,
      backgroundColor: isDarkMode ? colors.bglight : "transparent",
      alignItems: "center" as const,
      justifyContent: "center" as const,
    },
    cancelButtonText: {
      fontSize: 16,
      fontWeight: "500" as const,
      color: colors.text,
    },
    confirmButton: {
      flex: 1,
      paddingVertical: moderateScale(14),
      borderRadius: 12,
      backgroundColor: colors.stateerrorbase,
      alignItems: "center" as const,
      justifyContent: "center" as const,
      opacity: isLoading ? 0.7 : 1,
    },
    confirmButtonText: {
      fontSize: 16,
      fontWeight: "600" as const,
      color: colors.white,
    },
  };

  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={handleCancel}
    >
      <View style={modalStyles.overlay}>
        <View
          style={{
            width: "90%",
            backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
            borderRadius: 16,
            padding: moderateScale(24),
            elevation: 8,
          }}
        >
          <View style={modalStyles.header}>
            <View style={modalStyles.iconContainer}>
              <CustomIcon
                Icon={ICONS.DeleteAccountIcon}
                width={32}
                height={32}
              />
            </View>
            <Text style={modalStyles.title}>Deactivate Account</Text>
            <Text style={modalStyles.subtitle}>
              This action will permanently deactivate your account. Please enter
              your password to confirm.
            </Text>
          </View>

          {/* Password Input */}
          <View style={modalStyles.inputContainer}>
            <Text style={modalStyles.label}>
              Password <Text style={{ color: colors.stateerrorbase }}>*</Text>
            </Text>
            <View style={modalStyles.passwordInputContainer}>
              <CustomIcon Icon={ICONS.LockIcon} width={16} height={16} />
              <TextInput
                style={modalStyles.passwordInput}
                placeholder="Enter your password"
                placeholderTextColor={colors.greyText}
                secureTextEntry={secureText}
                value={password}
                onChangeText={setPassword}
                editable={!isLoading}
              />
              <TouchableOpacity
                style={modalStyles.eyeButton}
                onPress={() => setSecureText(!secureText)}
                disabled={isLoading}
              >
                <CustomIcon
                  Icon={secureText ? ICONS.EyeoffIcon : ICONS.EyeIcon}
                  width={20}
                  height={20}
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Buttons */}
          <View style={modalStyles.buttonContainer}>
            <TouchableOpacity
              style={modalStyles.cancelButton}
              onPress={handleCancel}
              disabled={isLoading}
              activeOpacity={0.8}
            >
              <Text style={modalStyles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={modalStyles.confirmButton}
              onPress={handleConfirm}
              disabled={isLoading || !password.trim()}
              activeOpacity={0.8}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color={colors.white} />
              ) : (
                <Text style={modalStyles.confirmButtonText}>Confirm</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default DeactivateAccountModal;

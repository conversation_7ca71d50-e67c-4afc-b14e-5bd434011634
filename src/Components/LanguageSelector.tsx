import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  Alert,
} from "react-native";
import { useLanguage, Language } from "../Localization/LanguageContext";
import { useTheme } from "../Utilities/ThemeContext";
import CustomIcon from "./CustomIcon";
import ICONS from "../Assets/Icon";
import useTranslation from "../Localization/useTranslation";

interface LanguageSelectorProps {
  showLabel?: boolean;
  style?: any;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  showLabel = true,
  style,
}) => {
  const { colors, isDarkMode } = useTheme();
  const { currentLanguage, changeLanguage, availableLanguages, isLoading } =
    useLanguage();
  const { common } = useTranslation();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);

  const currentLang = availableLanguages.find(
    (lang) => lang.code === currentLanguage
  );

  const handleLanguageChange = async (language: Language) => {
    try {
      setIsChangingLanguage(true);
      await changeLanguage(language.code);
      setIsModalVisible(false);
    } catch (error) {
      Alert.alert(
        common("error"),
        "Failed to change language. Please try again.",
        [{ text: common("ok") }]
      );
    } finally {
      setIsChangingLanguage(false);
    }
  };

  const renderLanguageItem = ({ item }: { item: Language }) => {
    const isSelected = item.code === currentLanguage;

    return (
      <TouchableOpacity
        style={[
          styles.languageItem,
          {
            backgroundColor: isSelected
              ? colors.primaryBase
              : isDarkMode
              ? colors.cardBackground
              : colors.white,
          },
        ]}
        onPress={() => handleLanguageChange(item)}
        disabled={isChangingLanguage}
      >
        <View style={styles.languageInfo}>
          <Text style={styles.flag}>{item.flag}</Text>
          <View style={styles.languageText}>
            <Text
              style={[
                styles.languageName,
                {
                  color: isSelected ? colors.white : colors.text,
                  fontWeight: isSelected ? "bold" : "normal",
                },
              ]}
            >
              {item.name}
            </Text>
            <Text
              style={[
                styles.nativeName,
                {
                  color: isSelected
                    ? colors.white
                    : isDarkMode
                    ? colors.greyText
                    : colors.greyText,
                },
              ]}
            >
              {item.nativeName}
            </Text>
          </View>
        </View>
        {isSelected && (
          <CustomIcon Icon={ICONS.CheckRightIcon} width={20} height={20} />
        )}
      </TouchableOpacity>
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.container, style]}>
        <Text style={[styles.loadingText, { color: colors.text }]}>
          {common("loading")}
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={[
          styles.selector,
          {
            backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
            borderColor: colors.border,
          },
        ]}
        onPress={() => setIsModalVisible(true)}
      >
        <View style={styles.currentLanguage}>
          <Text style={styles.flag}>{currentLang?.flag}</Text>
          {showLabel && (
            <Text style={[styles.currentLanguageText, { color: colors.text }]}>
              {currentLang?.name}
            </Text>
          )}
        </View>
        <CustomIcon Icon={ICONS.DropdownIcon} height={8} width={8} />
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={() => setIsModalVisible(false)}
        >
          <View
            style={[
              styles.modal,
              {
                backgroundColor: isDarkMode
                  ? colors.cardBackground
                  : colors.white,
                borderColor: colors.border,
              },
            ]}
          >
            <View style={styles.header}>
              <Text style={[styles.headerText, { color: colors.text }]}>
                {common("select")} Language
              </Text>
              <TouchableOpacity onPress={() => setIsModalVisible(false)}>
                <CustomIcon Icon={ICONS.CrossIcon} width={20} height={20} />
              </TouchableOpacity>
            </View>

            <FlatList
              data={availableLanguages}
              renderItem={renderLanguageItem}
              keyExtractor={(item) => item.code}
              showsVerticalScrollIndicator={false}
              style={styles.languageList}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // Container styles can be customized via props
  },
  selector: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    minWidth: 100,
    gap: 8,
  },
  currentLanguage: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  flag: {
    fontSize: 20,
  },
  currentLanguageText: {
    fontSize: 14,
    fontWeight: "500",
  },
  loadingText: {
    fontSize: 14,
    textAlign: "center",
  },
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modal: {
    width: 280,
    maxHeight: 400,
    borderRadius: 12,
    borderWidth: 1,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E5",
  },
  headerText: {
    fontSize: 18,
    fontWeight: "600",
  },
  languageList: {
    maxHeight: 300,
    borderRadius: 12,
  },
  languageItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  languageInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  languageText: {
    marginLeft: 12,
    flex: 1,
  },
  languageName: {
    fontSize: 16,
    fontWeight: "500",
  },
  nativeName: {
    fontSize: 14,
    marginTop: 2,
  },
});

export default LanguageSelector;

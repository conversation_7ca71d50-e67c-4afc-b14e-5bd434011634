import React, {
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Image,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { ScrollView as GestureScrollView } from "react-native-gesture-handler";
import { SafeAreaView } from "react-native-safe-area-context";
import { useTheme } from "../Utilities/ThemeContext";
import AddAppointmentModal from "../Utilities/Components/Modal/AddAppointmentModal";
import EditAppointmentModal from "../Utilities/Components/Modal/EditAppointmentModal";
import { useAppSelector } from "../Redux/store";
import { verticalScale } from "../Utilities/Styles/responsiveSize";
import { TeamMebersData } from "../Redux/slices/memberSlice";

interface CalendarProps {
  data: any[];
  selectedDate: string;
  onAddAppointment: (appointment: any) => void;
  onUpdateAppointment?: (appointment: any) => void;
  onDeleteAppointment?: (id: string) => void;
  viewMode: "Hourly" | "Weekly" | "Monthly";
}

const CalendarScreen: FC<CalendarProps> = ({
  data,
  selectedDate,
  onAddAppointment,
  onUpdateAppointment,
  onDeleteAppointment,
  viewMode,
}) => {
  // Constants
  const HOUR_HEIGHT = 175;
  const CLIENT_WIDTH = 175;
  const TIME_COLUMN_WIDTH = 60;

  // Create time slots for all 24 hours in 24-hour format
  const TIME_SLOTS = Array.from({ length: 24 }, (_, i) => {
    const hour = i.toString().padStart(2, "0");
    return `${hour}:00`;
  });

  // Updated time calculation functions
  const parseTime = (time: string) => {
    const [hours, minutes] = time.split(":").map(Number);
    return hours + minutes / 60;
  };

  const calculateDuration = (startTime: string, endTime: string) => {
    const start = parseTime(startTime);
    const end = parseTime(endTime);
    return (end - start) * HOUR_HEIGHT;
  };

  const timeToPosition = (time: string) => {
    const timeValue = parseTime(time);
    // No offset needed since we're showing all 24 hours starting from 00:00
    return timeValue * HOUR_HEIGHT;
  };

  // Function to determine card color based on status and theme
  const getCardColor = (status: string, isDarkMode: boolean) => {
    if (isDarkMode) {
      // Dark mode colors - more saturated and darker
      switch (status) {
        case "CANCELED":
          return "#661A1E"; // Dark red for canceled
        case "CONFIRMED":
          return "#0A5A3C"; // Dark green for confirmed
        default:
          return "#6FAEF1"; // Dark blue as default
      }
    } else {
      // Light mode colors - lighter and less saturated
      switch (status) {
        case "CANCELED":
          return "#FFEBEC"; // Light red for canceled
        case "CONFIRMED":
          return "#50fd9d"; // Light green for confirmed
        default:
          return "#6FAEF1"; // Light blue as default
      }
    }
  };

  // Function to determine card heading color based on status and theme
  const getCardHeadingColor = (status: string, isDarkMode: boolean) => {
    if (isDarkMode) {
      // Dark mode heading colors
      switch (status) {
        case "CANCELED":
          return "#FFFFFF"; // White for canceled in dark mode
        case "CONFIRMED":
          return "#FFFFFF"; // White for confirmed in dark mode
        default:
          return "#FFFFFF"; // White as default in dark mode
      }
    } else {
      // Light mode heading colors
      switch (status) {
        case "CANCELED":
          return "#FB3748"; // Red for canceled
        case "CONFIRMED":
          return "#0E121B"; // Dark for confirmed
        default:
          return "#0E121B"; // Dark as default
      }
    }
  };

  const getAccentColor = (status: string, isDarkMode: boolean) => {
    if (isDarkMode) {
      // Dark mode accent colors
      switch (status) {
        case "CANCELED":
          return "#FF6B6B"; // Brighter red for canceled in dark mode
        case "CONFIRMED":
          return "#4ECDC4"; // Teal for confirmed in dark mode
        default:
          return "#FFD166"; // Yellow-gold as default in dark mode
      }
    } else {
      // Light mode accent colors
      switch (status) {
        case "CANCELED":
          return "#FB3748"; // Red for canceled
        case "CONFIRMED":
          return "#6E7B91"; // Slate for confirmed
        default:
          return "#6E7B91"; // Slate as default
      }
    }
  };

  // Optimized AppointmentCard Component
  const AppointmentCard = React.memo(
    ({
      appointment,
      clientIndex,
      overlapIndex,
      totalOverlaps,
      onPress,
    }: any) => {
      const startPosition = timeToPosition(appointment.startTime);
      const duration = calculateDuration(
        appointment.startTime,
        appointment.endTime
      );

      // Adjust width and position for overlapping appointments
      const adjustedWidth = (CLIENT_WIDTH - 10) / (totalOverlaps || 1);
      const adjustedLeft =
        clientIndex * CLIENT_WIDTH + 5 + overlapIndex * adjustedWidth;

      const { colors, isDarkMode } = useTheme();
      const styles = useThemedCalendarStyles();

      return (
        <TouchableOpacity
          style={[
            styles.appointmentCard,
            {
              position: "absolute",
              left: adjustedLeft,
              top: startPosition,
              height: duration - 4, // Subtract 4 to create space
              width: adjustedWidth,
              backgroundColor: getCardColor(appointment.status, isDarkMode),
              marginBottom: 4,
              overflow: "hidden",
            },
          ]}
          onPress={() => onPress(appointment)}
          activeOpacity={0.7}
        >
          <View
            style={[
              styles.accentLine,
              {
                backgroundColor: getAccentColor(appointment.status, isDarkMode),
                height: duration / 3,
              },
            ]}
          />
          <Text
            style={[
              styles.appointmentTitle,
              {
                color: getCardHeadingColor(appointment.status, isDarkMode),
              },
            ]}
          >
            {appointment.title}
          </Text>
          <Text
            style={[
              styles.appointmentTime,
              { color: isDarkMode ? "rgba(255, 255, 255, 0.8)" : "#525866" },
            ]}
          >
            {`${appointment.startTime} - ${appointment.endTime}`}
          </Text>
          <Text
            style={[
              styles.appointmentService,
              { color: isDarkMode ? "rgba(255, 255, 255, 0.7)" : "#525866" },
            ]}
          >
            {appointment.service}
          </Text>
        </TouchableOpacity>
      );
    }
  );

  // State to force re-render when time updates
  const [, setTimeUpdate] = useState(0);
  // We only need the styles here, isDarkMode is passed to the AppointmentCard component
  const styles = useThemedCalendarStyles();

  // Get team members from Redux store
  const { members } = useAppSelector((state) => state.members);
  // const members = TeamMebersData;
  // State for appointment modals
  const [isAppointmentModalVisible, setIsAppointmentModalVisible] =
    useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [selectedClient, setSelectedClient] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [selectedTime, setSelectedTime] = useState<string>("");
  const [selectedAppointment, setSelectedAppointment] = useState<any>(null);

  const refs = {
    headerScroll: useRef<GestureScrollView>(null),
    contentScroll: useRef<GestureScrollView>(null),
    timeColumnScroll: useRef<GestureScrollView>(null),
    gridScroll: useRef<GestureScrollView>(null),
  };

  // Update time indicator every minute to force re-render
  useEffect(() => {
    const updateTime = () => setTimeUpdate((prev) => prev + 1);
    // Initial update
    updateTime();
    const interval = setInterval(updateTime, 30000); // Update every 30 seconds for more accuracy
    return () => clearInterval(interval);
  }, []);

  // Scroll to current time when component mounts
  useEffect(() => {
    // Wait for the component to fully render
    const timer = setTimeout(() => {
      const currentTime = new Date().getHours();
      // Scroll to current time with some offset to show a few hours before
      const scrollPosition = Math.max(0, (currentTime - 2) * HOUR_HEIGHT);

      if (refs.timeColumnScroll.current) {
        refs.timeColumnScroll.current.scrollTo({
          y: scrollPosition,
          animated: true,
        });
      }

      if (refs.gridScroll.current) {
        refs.gridScroll.current.scrollTo({ y: scrollPosition, animated: true });
      }
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const handleHorizontalScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const scrollX = event.nativeEvent.contentOffset.x;
      refs.headerScroll.current?.scrollTo({ x: scrollX, animated: false });
      refs.contentScroll.current?.scrollTo({ x: scrollX, animated: false });
    },
    []
  );

  const handleVerticalScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const scrollY = event.nativeEvent.contentOffset.y;
      refs.timeColumnScroll.current?.scrollTo({ y: scrollY, animated: false });
      refs.gridScroll.current?.scrollTo({ y: scrollY, animated: false });
    },
    []
  );

  // Group appointments by team member and detect overlaps
  const clientAppointments = useMemo(() => {
    const groupedByTeamMember = new Map<
      string,
      { apt: any; index: number; overlapIndex: number; totalOverlaps: number }[]
    >();

    // Initialize map for each team member
    members.forEach((member) => {
      groupedByTeamMember.set(member.id, []);
    });

    // Sort appointments by start time
    const sortedAppointments = [...data].sort((a, b) => {
      const aTime = new Date(`1970-01-01T${a.startTime}:00`).getTime();
      const bTime = new Date(`1970-01-01T${b.startTime}:00`).getTime();
      return aTime - bTime;
    });

    // Detect overlaps and assign positions
    sortedAppointments.forEach((apt) => {
      // Use teamMemberId instead of clientId
      const teamMemberIndex = members.findIndex(
        (m) => m.id === apt.teamMemberId
      );
      if (teamMemberIndex === -1) return;

      const teamMemberApts = groupedByTeamMember.get(apt.teamMemberId) || [];
      let overlapIndex = 0;
      let totalOverlaps = 1;

      // Check for overlaps with existing appointments for this team member
      for (const existing of teamMemberApts) {
        const existingStart = new Date(
          `1970-01-01T${existing.apt.startTime}:00`
        ).getTime();
        const existingEnd = new Date(
          `1970-01-01T${existing.apt.endTime}:00`
        ).getTime();
        const currentStart = new Date(
          `1970-01-01T${apt.startTime}:00`
        ).getTime();
        const currentEnd = new Date(`1970-01-01T${apt.endTime}:00`).getTime();

        if (currentStart < existingEnd && currentEnd > existingStart) {
          overlapIndex = Math.max(overlapIndex, existing.overlapIndex + 1);
          totalOverlaps = Math.max(totalOverlaps, overlapIndex + 1);
        }
      }

      // Update totalOverlaps for all overlapping appointments
      teamMemberApts.forEach((item) => {
        const existingStart = new Date(
          `1970-01-01T${item.apt.startTime}:00`
        ).getTime();
        const existingEnd = new Date(
          `1970-01-01T${item.apt.endTime}:00`
        ).getTime();
        const currentStart = new Date(
          `1970-01-01T${apt.startTime}:00`
        ).getTime();
        const currentEnd = new Date(`1970-01-01T${apt.endTime}:00`).getTime();

        if (currentStart < existingEnd && currentEnd > existingStart) {
          item.totalOverlaps = totalOverlaps;
        }
      });

      teamMemberApts.push({
        apt,
        index: teamMemberIndex,
        overlapIndex,
        totalOverlaps,
      });
      groupedByTeamMember.set(apt.teamMemberId, teamMemberApts);
    });

    // Flatten the map into an array
    return Array.from(groupedByTeamMember.values())
      .flat()
      .filter((item: any) => item.apt); // Filter out empty entries
  }, [data]);

  const gridHeight = TIME_SLOTS.length * HOUR_HEIGHT; // Total height of the grid

  // Calculate current time position and format current time
  const getCurrentTimePosition = () => {
    const now = new Date();

    // Optional: Add 30 minutes to show a bit ahead of current time
    // now.setMinutes(now.getMinutes() + 30);

    const localHours = now.getHours();
    const localMinutes = now.getMinutes();
    const localSeconds = now.getSeconds();

    const timeValue = localHours + localMinutes / 60 + localSeconds / 3600;

    // Always show the current time line since we're displaying all 24 hours
    return timeValue * HOUR_HEIGHT;
  };

  const getCurrentTimeString = () => {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, "0");
    const minutes = now.getMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
  };

  const currentTimePosition = getCurrentTimePosition();
  const currentTimeString = getCurrentTimeString();

  const scrollViewProps = Platform.select({
    android: {
      nestedScrollEnabled: true,
      showsHorizontalScrollIndicator: false,
      showsVerticalScrollIndicator: false,
      overScrollMode: "never" as const,
      bounces: false,
    },
    default: {
      showsHorizontalScrollIndicator: false,
      showsVerticalScrollIndicator: false,
      bounces: false,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View
          style={[
            styles.timeColumnHeader,
            {
              width: TIME_COLUMN_WIDTH,
            },
          ]}
        />
        <GestureScrollView
          ref={refs.headerScroll}
          horizontal
          {...scrollViewProps}
          onMomentumScrollEnd={handleHorizontalScroll}
        >
          {members.map((member) => (
            <View
              key={member.id}
              style={[
                styles.clientProfile,
                {
                  width: CLIENT_WIDTH,
                },
              ]}
            >
              <Image
                source={{ uri: member.image }}
                style={styles.clientImage}
              />
              <Text style={styles.clientName}>{member.name}</Text>
            </View>
          ))}
        </GestureScrollView>
      </View>

      <View style={styles.mainContent}>
        <GestureScrollView
          ref={refs.timeColumnScroll}
          {...scrollViewProps}
          onMomentumScrollEnd={handleVerticalScroll}
          contentContainerStyle={{ minWidth: TIME_COLUMN_WIDTH + 10 }}
        >
          <View
            style={[
              styles.timeColumn,
              {
                width: TIME_COLUMN_WIDTH,
              },
            ]}
          >
            {TIME_SLOTS.map((time) => (
              <View
                key={time}
                style={[
                  styles.timeSlot,
                  {
                    height: HOUR_HEIGHT,
                  },
                ]}
              >
                <Text style={styles.timeText}>{time}</Text>
              </View>
            ))}
          </View>
        </GestureScrollView>

        <GestureScrollView
          ref={refs.contentScroll}
          horizontal
          {...scrollViewProps}
          onMomentumScrollEnd={handleHorizontalScroll}
        >
          <GestureScrollView
            ref={refs.gridScroll}
            {...scrollViewProps}
            onMomentumScrollEnd={handleVerticalScroll}
          >
            <View
              style={[
                styles.gridContainer,
                { width: members.length * CLIENT_WIDTH },
              ]}
            >
              {TIME_SLOTS.map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.gridLine,
                    {
                      height: HOUR_HEIGHT,
                    },
                  ]}
                />
              ))}
              {/* Vertical Team Member Lines */}
              {members.map((_, teamMemberIndex) => (
                <View
                  key={`vline-${teamMemberIndex}`}
                  style={[
                    styles.verticalLine,
                    {
                      left: teamMemberIndex * CLIENT_WIDTH,
                      height: gridHeight,
                    },
                  ]}
                />
              ))}

              {/* Clickable grid cells for adding appointments */}
              {TIME_SLOTS.map((time, timeIndex) => (
                <React.Fragment key={`grid-${timeIndex}`}>
                  {members.map((member, teamMemberIndex) => {
                    // Get all appointments for this team member in this hour
                    const hourAppointments = clientAppointments.filter(
                      (item: any) =>
                        item.index === teamMemberIndex &&
                        item.apt.startTime.split(":")[0] === time.split(":")[0]
                    );

                    // Check if the hour is completely filled
                    const hasAppointment = hourAppointments.length > 0;

                    // Check if there's still space for more appointments
                    const hasAvailableSpace =
                      hasAppointment && hourAppointments.length < 2;

                    return (
                      <TouchableOpacity
                        key={`cell-${timeIndex}-${teamMemberIndex}`}
                        style={{
                          position: "absolute",
                          left: teamMemberIndex * CLIENT_WIDTH,
                          top: timeIndex * HOUR_HEIGHT,
                          width: CLIENT_WIDTH,
                          height: HOUR_HEIGHT,
                          zIndex: hasAppointment ? 0 : 1, // Lower z-index if there's an appointment
                          // Show a subtle indicator if there's space for more appointments
                          backgroundColor: hasAvailableSpace
                            ? "rgba(0, 122, 255, 0.05)"
                            : "transparent",
                        }}
                        disabled={hasAppointment && !hasAvailableSpace}
                        onPress={() => {
                          setSelectedClient(member);

                          // If there are existing appointments, suggest a time that doesn't conflict
                          if (hasAppointment && hasAvailableSpace) {
                            // Find available time slots in this hour
                            const hourStart = parseInt(time.split(":")[0]);
                            const existingSlots = hourAppointments.map(
                              ({ apt }) => {
                                const startMinutes = parseInt(
                                  apt.startTime.split(":")[1]
                                );
                                const endMinutes = parseInt(
                                  apt.endTime.split(":")[1]
                                );
                                const endHour = parseInt(
                                  apt.endTime.split(":")[0]
                                );

                                return {
                                  start: startMinutes,
                                  end: endHour > hourStart ? 60 : endMinutes,
                                };
                              }
                            );

                            // Find a 15-minute slot that doesn't overlap with existing appointments
                            let availableSlot = null;
                            const slotDuration = 15; // 15-minute slots

                            for (
                              let minute = 0;
                              minute < 60;
                              minute += slotDuration
                            ) {
                              const slotEnd = minute + slotDuration;
                              const isOverlapping = existingSlots.some(
                                (slot) =>
                                  minute < slot.end && slotEnd > slot.start
                              );

                              if (!isOverlapping) {
                                availableSlot = `${hourStart
                                  .toString()
                                  .padStart(2, "0")}:${minute
                                  .toString()
                                  .padStart(2, "0")}`;
                                break;
                              }
                            }

                            // Use the available slot or default to the hour
                            setSelectedTime(availableSlot || time);
                          } else {
                            setSelectedTime(time);
                          }

                          setIsAppointmentModalVisible(true);
                        }}
                      />
                    );
                  })}
                </React.Fragment>
              ))}
              {clientAppointments.map(
                ({ apt, index, overlapIndex, totalOverlaps }) => (
                  <AppointmentCard
                    key={apt.id}
                    appointment={apt}
                    clientIndex={index}
                    overlapIndex={overlapIndex}
                    totalOverlaps={totalOverlaps}
                    onPress={(appointment: any) => {
                      setSelectedAppointment(appointment);
                      setIsEditModalVisible(true);
                    }}
                  />
                )
              )}

              {/* Current time line - always visible with 24-hour format */}
              <View
                style={[
                  styles.currentTimeLine,
                  {
                    top: currentTimePosition,
                    width: members.length * CLIENT_WIDTH,
                  },
                ]}
              >
                <View style={styles.currentTimeIndicator} />
                <Text style={styles.currentTimeText}>{currentTimeString}</Text>
              </View>
            </View>
          </GestureScrollView>
        </GestureScrollView>
      </View>

      {/* Add Appointment Modal */}
      {isAppointmentModalVisible && selectedClient && (
        <AddAppointmentModal
          visible={isAppointmentModalVisible}
          onClose={() => setIsAppointmentModalVisible(false)}
          clientId={selectedClient.id}
          clientName={selectedClient.name}
          startTime={selectedTime}
          date={selectedDate}
          onAddAppointment={onAddAppointment}
        />
      )}

      {/* Edit Appointment Modal */}
      {isEditModalVisible && selectedAppointment && (
        <EditAppointmentModal
          visible={isEditModalVisible}
          onClose={() => setIsEditModalVisible(false)}
          appointment={selectedAppointment}
          onUpdateAppointment={(updatedAppointment) => {
            if (onUpdateAppointment) {
              onUpdateAppointment(updatedAppointment);
            }
          }}
          onDeleteAppointment={(id) => {
            if (onDeleteAppointment) {
              onDeleteAppointment(id);
            }
          }}
        />
      )}
    </SafeAreaView>
  );
};

export default CalendarScreen;

export const useThemedCalendarStyles = () => {
  const { colors, isDarkMode } = useTheme();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDarkMode ? colors.background : "#f5f5f5",
    },
    header: {
      flexDirection: "row",
      backgroundColor: isDarkMode ? colors.black : colors.white,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    timeColumnHeader: {
      height: 80,
    },
    clientProfile: {
      alignItems: "center",
      padding: 10,
      borderLeftWidth: 1,
      borderLeftColor: colors.border,
    },
    clientImage: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginBottom: 5,
    },
    clientName: {
      fontSize: 12,
      textAlign: "center",
      color: colors.text,
    },
    mainContent: {
      flex: 1,
      flexDirection: "row",
    },
    timeColumn: {
      backgroundColor: isDarkMode ? colors.black : colors.white,
      borderRightWidth: 1,
      borderRightColor: colors.border,
    },
    timeSlot: {
      justifyContent: "center",
      alignItems: "center",
    },
    timeText: {
      fontSize: 12,
      color: colors.greyText,
      textAlign: "center",
    },
    gridContainer: {
      position: "relative",
      backgroundColor: isDarkMode ? colors.black : colors.white,
    },
    gridLine: {
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    verticalLine: {
      position: "absolute",
      width: 1,
      backgroundColor: colors.border,
      top: 0,
    },
    appointmentCard: {
      backgroundColor: "#007AFF",
      borderRadius: 8,
      padding: 8,
      shadowColor: isDarkMode ? "#000" : "#333",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDarkMode ? 0.5 : 0.2,
      shadowRadius: 3,
      elevation: 4,
      borderWidth: isDarkMode ? 1 : 0,
      borderColor: isDarkMode ? "rgba(255, 255, 255, 0.1)" : "transparent",
    },
    appointmentTitle: {
      fontSize: 11,
      fontWeight: "bold",
    },
    appointmentTime: {
      fontSize: 9,
      color: isDarkMode ? "rgba(255, 255, 255, 0.8)" : "#525866",
      marginBottom: 2,
    },
    appointmentService: {
      fontSize: 10,
      color: isDarkMode ? "rgba(255, 255, 255, 0.7)" : "#525866",
      fontWeight: "500",
    },
    appointmentTeamMember: {
      fontSize: 11,
      color: isDarkMode ? "rgba(255, 255, 255, 0.6)" : "#525866",
    },
    accentLine: {
      width: 4,
      position: "absolute",
      left: -2,
      top: 8,
      borderRadius: 2,
    },
    currentTimeLine: {
      position: "absolute",
      height: 1,
      backgroundColor: colors.BaseColor,
      zIndex: 10,
    },
    currentTimeIndicator: {
      position: "absolute",
      top: -4,
      left: 0,
      height: 10,
      width: 10,
      borderRadius: 100,
      backgroundColor: colors.BaseColor,
    },
    currentTimeText: {
      position: "absolute",
      top: verticalScale(-11),
      left: 15,
      fontSize: 12,
      fontWeight: "600",
      color: colors.BaseColor,
      backgroundColor: isDarkMode ? colors.black : colors.white,
      paddingHorizontal: 4,
      paddingVertical: 2,
      borderRadius: 4,
      borderWidth: 1,
      borderColor: colors.BaseColor,
    },
  });
};

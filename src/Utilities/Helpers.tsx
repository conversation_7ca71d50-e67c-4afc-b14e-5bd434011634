import moment from "moment";
import AsyncStorage from "@react-native-async-storage/async-storage";

const HOLIDAYS = ["2025-12-25", "2025-01-01"]; // Example holiday dates (Christmas, New Year)

export const getCurrentWeek = () => {
  const startOfWeek = moment().startOf("isoWeek"); // Monday as the start
  return Array.from({ length: 7 }, (_, i) => {
    const date = startOfWeek.clone().add(i, "days");
    const formattedDate = date.format("YYYY-MM-DD");
    const isWeekend = date.isoWeekday() === 6 || date.isoWeekday() === 7; // Saturday & Sunday
    const isHoliday = HOLIDAYS.includes(formattedDate);

    return {
      date,
      formattedDate,
      isToday: date.isSame(moment(), "day"),
      isFaded: isWeekend || isHoliday, // Mark weekends and holidays as faded
    };
  });
};

export const getCurrentMonth = () => {
  const today = moment();
  return getMonthData(today);
};

export const getMonthData = (targetDate: moment.Moment) => {
  const today = moment();
  const startOfMonth = targetDate.clone().startOf("month");

  // Get the day of the week for the first day of the month (0-6, where 0 is Sunday)
  const firstDayOfMonth = startOfMonth.day();

  // Calculate how many days from the previous month we need to show
  const daysFromPrevMonth = firstDayOfMonth === 0 ? 6 : firstDayOfMonth - 1; // Adjust for Monday as first day

  // Start date will be the first day shown on the calendar (might be from previous month)
  const startDate = startOfMonth.clone().subtract(daysFromPrevMonth, "days");

  // We'll show 6 weeks (42 days) to ensure we cover the whole month
  const days = Array.from({ length: 42 }, (_, i) => {
    const date = startDate.clone().add(i, "days");
    const formattedDate = date.format("YYYY-MM-DD");
    const isWeekend = date.isoWeekday() === 6 || date.isoWeekday() === 7; // Saturday & Sunday
    const isHoliday = HOLIDAYS.includes(formattedDate);
    const isCurrentMonth =
      date.month() === targetDate.month() && date.year() === targetDate.year();
    const isToday = date.isSame(today, "day");

    return {
      date,
      formattedDate,
      isToday,
      isFaded: isWeekend || isHoliday || !isCurrentMonth, // Mark weekends, holidays, and days from other months as faded
      isCurrentMonth,
      // Add index to help with scrolling to today
      index: i,
    };
  });

  // Find the index of today for scrolling purposes (only if we're viewing the current month)
  const todayIndex = targetDate.isSame(today, "month")
    ? days.findIndex((day) => day.isToday)
    : -1;

  // Add a property to the array to indicate the index of today
  return {
    days,
    todayIndex: todayIndex !== -1 ? todayIndex : -1,
    monthName: targetDate.format("MMMM YYYY"),
    monthKey: targetDate.format("YYYY-MM"),
  };
};

// Function to calculate duration between startTime and endTime
export const calculateDuration = (startTime: string, endTime: string) => {
  const start = moment(startTime, "HH:mm");
  const end = moment(endTime, "HH:mm");
  const durationMinutes = end.diff(start, "minutes");

  if (durationMinutes >= 60) {
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    return `${hours} hr${hours > 1 ? "s" : ""}${
      minutes > 0 ? ` ${minutes} min${minutes > 1 ? "s" : ""}` : ""
    }`;
  }
  return `${durationMinutes} min${durationMinutes !== 1 ? "s" : ""}`;
};

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const getLocalStorageData = async (key: string) => {
  const value = await AsyncStorage.getItem(key);
  if (!value) return null; // Return null if no value exists
  try {
    return JSON.parse(value); // Try to parse as JSON
  } catch (error) {
    console.warn(
      `Could not parse "${key}" as JSON, returning raw value:`,
      value
    );
    return value; // Return the raw string if parsing fails
  }
};

export const storeLocalStorageData = async (key: string, value: any) => {
  await AsyncStorage.setItem(key, JSON.stringify(value)); // Always store as JSON string
};

export const deleteLocalStorageData = async (key: string) => {
  await AsyncStorage.removeItem(key); // Always store as JSON string
};

export function formatDateToYYYYMMDD(date: Date) {
  const year = date.getFullYear();

  // Pad month and day with leading zeros if needed
  const month = String(date.getMonth() + 1).padStart(2, "0"); // getMonth() returns 0-11
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
}

export function parseDateFromYYYYMMDD(dateString: string) {
  const [year, month, day] = dateString.split("-").map(Number);
  return new Date(year, month - 1, day); // month is 0-indexed in JS Date
}

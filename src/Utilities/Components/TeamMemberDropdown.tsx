import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  Image,
} from 'react-native';
import { useTheme } from '../ThemeContext';
import CustomIcon from '../../Components/CustomIcon';
import ICONS from '../../Assets/Icon';
import { moderateScale, verticalScale } from '../Styles/responsiveSize';

interface TeamMemberDropdownProps {
  selectedMember: string | null;
  onSelectMember: (memberId: string, memberName: string) => void;
  teamMembers?: any[];
}

const TeamMemberDropdown: React.FC<TeamMemberDropdownProps> = ({
  selectedMember,
  onSelectMember,
  teamMembers,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { colors, isDarkMode } = useTheme();

  // Helper functions to handle both old and new team member structures
  const getMemberId = (member: any): string => {
    return member.id || member._id;
  };

  const getMemberName = (member: any): string => {
    return member.name;
  };

  const getMemberEmail = (member: any): string => {
    return member.email;
  };

  const getMemberImage = (member: any): string => {
    return member.image || member.profilePicture || "";
  };

  // Find the selected member's name
  const selectedMemberName =
    selectedMember && teamMembers
      ? getMemberName(
          teamMembers.find(
            (member) => getMemberId(member) === selectedMember
          ) || {}
        )
      : "";

  return (
    <>
      <TouchableOpacity
        style={[
          styles.dropdownButton,
          {
            backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
            borderColor: colors.border,
          },
        ]}
        onPress={() => setIsModalVisible(true)}
      >
        <Text
          style={[
            styles.dropdownButtonText,
            { color: selectedMember ? colors.text : colors.greyText },
          ]}
        >
          {selectedMemberName || "Select team member"}
        </Text>
        <CustomIcon Icon={ICONS.ArrowDownIcon} height={12} width={12} />
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsModalVisible(false)}
        >
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode
                  ? colors.cardBackground
                  : colors.white,
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Select Team Member
              </Text>
              <TouchableOpacity onPress={() => setIsModalVisible(false)}>
                <CustomIcon Icon={ICONS.CrossIcon} height={15} width={15} />
              </TouchableOpacity>
            </View>

            <FlatList
              data={teamMembers || []}
              keyExtractor={(item) => getMemberId(item)}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.memberItem,
                    selectedMember === getMemberId(item) && {
                      backgroundColor: isDarkMode
                        ? colors.bglight
                        : colors.bgsoft,
                    },
                  ]}
                  onPress={() => {
                    onSelectMember(getMemberId(item), getMemberName(item));
                    setIsModalVisible(false);
                  }}
                >
                  {getMemberImage(item) && (
                    <Image
                      source={{ uri: getMemberImage(item) }}
                      style={styles.memberImage}
                    />
                  )}
                  <View style={styles.memberInfo}>
                    <Text style={[styles.memberName, { color: colors.text }]}>
                      {getMemberName(item)}
                    </Text>
                    <Text
                      style={[styles.memberEmail, { color: colors.greyText }]}
                    >
                      {getMemberEmail(item)}
                    </Text>
                  </View>
                  {selectedMember === getMemberId(item) && (
                    <CustomIcon
                      Icon={ICONS.CheckRightIcon}
                      height={15}
                      width={15}
                    />
                  )}
                </TouchableOpacity>
              )}
              ListEmptyComponent={() => (
                <Text style={[styles.emptyText, { color: colors.greyText }]}>
                  No team members available
                </Text>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 10,
    padding: 10,
    paddingHorizontal: 15,
  },
  dropdownButtonText: {
    fontSize: 14,
    fontWeight: '400',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '70%',
    borderRadius: 15,
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(15),
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '500',
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  memberImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 14,
    fontWeight: '500',
  },
  memberEmail: {
    fontSize: 12,
    marginTop: 5,
  },
  emptyText: {
    textAlign: 'center',
    padding: 20,
    fontSize: 14,
  },
});

export default TeamMemberDropdown;

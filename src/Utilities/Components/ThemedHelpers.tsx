import { TouchableOpacity } from 'react-native';
import CountryPicker, {
  CountryCode,
  Country,
} from 'react-native-country-picker-modal';

import { useTheme } from '../ThemeContext';
import CustomIcon from '../../Components/CustomIcon';
import ICONS from '../../Assets/Icon';
import styles from './style';

interface ThemedPhonePickerProps {
  visible: boolean;
  onSelect: (country: Country) => void;
  onClose?: () => void;
  countryCode?: CountryCode;
  onPress: () => void;
  border?: boolean;
}

export function ThemedPhonePicker({
  visible,
  onSelect,
  onClose,
  onPress,
  countryCode = "IN",
  border,
}: ThemedPhonePickerProps) {
  const { colors, isDarkMode } = useTheme();
  
  return (
    <TouchableOpacity
      style={[styles.picVw, { borderColor: colors.border }]}
      onPress={onPress}
    >
      <CountryPicker
        key={countryCode}
        visible={visible}
        onSelect={onSelect}
        onClose={onClose}
        theme={{
          fontSize: 14,
          fontFamily: "System",
          onBackgroundTextColor: isDarkMode ? colors.white : colors.maintext,
          backgroundColor: isDarkMode ? colors.cardBackground : colors.white,
          primaryColor: colors.primaryBase,
          primaryColorVariant: colors.primaryBase,
          flagSizeButton: 18,
        }}
        withCallingCode={true}
        withCallingCodeButton
        withFlagButton={false}
        withFilter
        countryCode={countryCode}
        containerButtonStyle={[
          {
            borderWidth: border == true ? 1 : 0,
            borderColor: colors.border,
          },
          styles.pickerContainer,
        ]}
      />
      <CustomIcon Icon={ICONS.DropdownIcon} height={6} width={10} />
    </TouchableOpacity>
  );
}

import moment from "moment";
import React, { useState } from "react";
import {
  Keyboard,
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import DatePicker from "react-native-date-picker";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import {
  useClients,
  useServices,
  useTeamMembers,
} from "../../../Hooks/useAppData";
import { Appointment } from "../../../Redux/slices/appointmentSlice";
import ClientSelectionModal from "../../Components/ClientSelectionModal";
import ServiceDropdown from "../../Components/ServiceDropdown";
import TeamMemberDropdown from "../../Components/TeamMemberDropdown";
import { moderateScale } from "../../Styles/responsiveSize";
import { useTheme } from "../../ThemeContext";
import Toast from "react-native-toast-message";
import { putData } from "../../../Services/ApiService";
import ENDPOINTS from "../../../Services/EndPoints";

interface EditAppointmentModalProps {
  visible: boolean;
  onClose: () => void;
  appointment: Appointment;
  onUpdateAppointment: (appointment: Appointment) => void;
  onDeleteAppointment: (id: string) => void;
}

const EditAppointmentModal: React.FC<EditAppointmentModalProps> = ({
  visible,
  onClose,
  appointment,
  onUpdateAppointment,
  onDeleteAppointment,
}) => {
  const { colors, isDarkMode } = useTheme();

  // Calculate default end time (30 minutes after start time)
  const calculateDefaultEndTime = (start: string) => {
    const [hours, minutes] = start.split(":").map(Number);
    const startDate = new Date();
    startDate.setHours(hours, minutes, 0, 0);
    startDate.setMinutes(startDate.getMinutes() + 30);
    return `${String(startDate.getHours()).padStart(2, "0")}:${String(
      startDate.getMinutes()
    ).padStart(2, "0")}`;
  };

  // Get team members, services, and clients using new data management hooks
  const { clients } = useClients();
  const { members } = useTeamMembers();
  const { services: categoriesWithServices } = useServices();

  // State for form fields - initialize with appointment data
  const [selectedClientId, setSelectedClientId] = useState<string | null>(
    appointment.clientId || null
  );

  const [title, setTitle] = useState(appointment.title);
  const [phoneNumber, setPhoneNumber] = useState(appointment.clientPhone || "");
  const [serviceId, setServiceId] = useState<string | null>(null);
  const [serviceName, setServiceName] = useState(appointment.service);
  const [serviceDuration, setServiceDuration] = useState("");
  const [teamMemberId, setTeamMemberId] = useState<string | null>(null);
  const [teamMemberName, setTeamMemberName] = useState(appointment.teamMember);
  const [appointmentStartTime, setAppointmentStartTime] = useState(
    appointment.startTime
  );
  const [appointmentEndTime, setAppointmentEndTime] = useState(
    appointment.endTime
  );
  const [appointmentDate, setAppointmentDate] = useState(appointment.date);
  const [status, setStatus] = useState(appointment.status);
  const [isStartTimePickerOpen, setIsStartTimePickerOpen] = useState(false);
  const [isEndTimePickerOpen, setIsEndTimePickerOpen] = useState(false);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  // Find the service and team member IDs based on names
  React.useEffect(() => {
    // Find service ID if categoriesWithServices is available
    if (categoriesWithServices && Array.isArray(categoriesWithServices)) {
      // Flatten all services from all categories
      const allServices = categoriesWithServices.flatMap(
        (category: any) => category.services || []
      );

      // Find service by name (check both old and new structure)
      const service = allServices.find((s: any) => {
        const serviceName = s.businessName || s.name;
        return serviceName === appointment.service;
      });

      if (service) {
        const serviceId = service.id || service._id;
        const serviceDuration =
          typeof service.duration === "string"
            ? service.duration
            : `${service.duration} min`;

        setServiceId(serviceId);
        setServiceDuration(serviceDuration);
      }
    }

    // Find team member ID if members is available
    if (members && Array.isArray(members)) {
      const member = members.find(
        (m: any) => m.name === appointment.teamMember
      );
      if (member) {
        const memberId = member.id || member._id;
        setTeamMemberId(memberId);
      }
    }
  }, [appointment, categoriesWithServices, members]);

  // Handle client selection
  const handleSelectClient = (id: string, name: string, phone: string) => {
    setSelectedClientId(id);
    setTitle(name);
    setPhoneNumber(phone);
  };

  // State for client selection modal
  const [isClientModalVisible, setIsClientModalVisible] = useState(false);

  // Open client selection modal
  const openClientSelectionModal = () => {
    setIsClientModalVisible(true);
  };

  // Handle service selection
  const handleSelectService = (id: string, name: string, duration: string) => {
    setServiceId(id);
    setServiceName(name);
    setServiceDuration(duration);

    // Update end time based on service duration
    if (duration) {
      const durationMinutes = parseInt(duration.split(" ")[0]);
      if (!isNaN(durationMinutes)) {
        const [hours, minutes] = appointmentStartTime.split(":").map(Number);
        const startDate = new Date();
        startDate.setHours(hours, minutes, 0, 0);
        startDate.setMinutes(startDate.getMinutes() + durationMinutes);
        setAppointmentEndTime(
          `${String(startDate.getHours()).padStart(2, "0")}:${String(
            startDate.getMinutes()
          ).padStart(2, "0")}`
        );
      }
    }
  };

  // Convert string time to Date object for the time picker
  const timeStringToDate = (timeString: string) => {
    const [hours, minutes] = timeString.split(":").map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    return date;
  };

  // Handle form submission
  const handleUpdateAppointment = async () => {
    if (!title || !serviceId || !teamMemberId) {
      // Show validation error
      return;
    }

    // Validate client information
    if (!phoneNumber && !selectedClientId) {
      // Show validation error for client information
      return;
    }

    try {
      const response = await putData(
        `${ENDPOINTS.updateAppointment}/${appointment.id}`,
        {
          clientId: selectedClientId,
          teamMemberId: teamMemberId,
          startDate: moment(appointmentDate).format("YYYY-MM-DD"),
          startTime: appointmentStartTime,
          endTime: appointmentEndTime,
          serviceIds: [serviceId],
          status: status,
        }
      );

      if (response.data.success) {
        const updatedAppointment: Appointment = {
          ...appointment,
          clientId: selectedClientId || appointment.clientId || "",
          clientName: title,
          clientPhone: phoneNumber,
          startTime: appointmentStartTime,
          endTime: appointmentEndTime,
          title,
          status,
          date: appointmentDate,
          service: serviceName,
          teamMember: teamMemberName,
          teamMemberId: teamMemberId || "",
        };

        onUpdateAppointment(updatedAppointment);
        Toast.show({
          type: "success",
          text1: response.data.message,
        });
        onClose();
      }
    } catch (error: any) {
      console.error("Error updating appointment:", error);
      Toast.show({
        type: "error",
        text1: error.message || "Something went wrong. Please try again.",
      });
    }
  };

  // Handle appointment deletion
  const handleCancelAppointment = () => {
    onDeleteAppointment(appointment.id);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.modalContainer}
      >
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={() => {
            Keyboard.dismiss();
            onClose();
          }}
        />
        <View
          style={[
            styles.modalContent,
            {
              backgroundColor: isDarkMode
                ? colors.cardBackground
                : colors.white,
            },
          ]}
        >
          <ScrollView
            bounces={false}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* Header */}
            <View style={styles.header}>
              <View />
              <Text style={[styles.headerTitle, { color: colors.text }]}>
                Edit Appointment
              </Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <CustomIcon Icon={ICONS.CrossIcon} height={15} width={15} />
              </TouchableOpacity>
            </View>

            {/* Form Fields */}
            <View style={styles.formContainer}>
              {/* Client Selection */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  Client <Text style={styles.required}>*</Text>
                </Text>
                <View
                  style={[
                    styles.inputContainer,
                    {
                      backgroundColor: isDarkMode
                        ? colors.cardBackground
                        : colors.white,
                      borderColor: colors.border,
                      marginBottom: 10,
                    },
                  ]}
                >
                  <TextInput
                    style={[styles.input, { color: colors.text }]}
                    value={title}
                    onChangeText={setTitle}
                    placeholder="Client name"
                    placeholderTextColor={colors.greyText}
                  />
                </View>
                <View
                  style={[
                    styles.inputContainer,
                    {
                      backgroundColor: isDarkMode
                        ? colors.cardBackground
                        : colors.white,
                      borderColor: colors.border,
                    },
                  ]}
                >
                  <TextInput
                    style={[styles.input, { color: colors.text }]}
                    value={phoneNumber}
                    onChangeText={setPhoneNumber}
                    placeholder="Phone number"
                    placeholderTextColor={colors.greyText}
                    keyboardType="phone-pad"
                  />
                </View>
                <TouchableOpacity
                  style={styles.backToSelectionButton}
                  onPress={openClientSelectionModal}
                >
                  <Text style={{ color: colors.primaryBase }}>
                    Select from existing clients
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Client Selection Modal */}
              <ClientSelectionModal
                visible={isClientModalVisible}
                onClose={() => setIsClientModalVisible(false)}
                onSelectClient={handleSelectClient}
                clients={clients}
                selectedClient={selectedClientId}
                onAddNewClient={() => setIsClientModalVisible(false)}
              />

              {/* Service Selection */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  Service <Text style={styles.required}>*</Text>
                </Text>
                <ServiceDropdown
                  selectedService={serviceId}
                  onSelectService={handleSelectService}
                  services={
                    categoriesWithServices?.flatMap(
                      (category: any) => category.services || []
                    ) || []
                  }
                />
              </View>

              {/* Team Member Selection */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  Team Member <Text style={styles.required}>*</Text>
                </Text>
                <TeamMemberDropdown
                  selectedMember={teamMemberId}
                  onSelectMember={(id, name) => {
                    setTeamMemberId(id);
                    setTeamMemberName(name);
                  }}
                  teamMembers={members}
                />
              </View>

              {/* Date Selection */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  Date <Text style={styles.required}>*</Text>
                </Text>
                <TouchableOpacity
                  style={[
                    styles.timeSelector,
                    {
                      backgroundColor: isDarkMode
                        ? colors.cardBackground
                        : colors.white,
                      borderColor: colors.border,
                    },
                  ]}
                  onPress={() => setIsDatePickerOpen(true)}
                >
                  <CustomIcon Icon={ICONS.Calender} width={20} height={20} />
                  <Text style={[styles.timeText, { color: colors.text }]}>
                    {moment(appointmentDate).format("MMM DD, YYYY")}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Time Selection */}
              <View style={styles.timeContainer}>
                {/* Start Time */}
                <View style={[styles.timeInput, { flex: 1 }]}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    Start Time <Text style={styles.required}>*</Text>
                  </Text>
                  <TouchableOpacity
                    style={[
                      styles.timeSelector,
                      {
                        backgroundColor: isDarkMode
                          ? colors.cardBackground
                          : colors.white,
                        borderColor: colors.border,
                      },
                    ]}
                    onPress={() => setIsStartTimePickerOpen(true)}
                  >
                    <CustomIcon Icon={ICONS.Calender} width={20} height={20} />
                    <Text style={[styles.timeText, { color: colors.text }]}>
                      {appointmentStartTime}
                    </Text>
                  </TouchableOpacity>
                </View>

                {/* End Time */}
                <View style={[styles.timeInput, { flex: 1 }]}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    End Time <Text style={styles.required}>*</Text>
                  </Text>
                  <TouchableOpacity
                    style={[
                      styles.timeSelector,
                      {
                        backgroundColor: isDarkMode
                          ? colors.cardBackground
                          : colors.white,
                        borderColor: colors.border,
                      },
                    ]}
                    onPress={() => setIsEndTimePickerOpen(true)}
                  >
                    <CustomIcon Icon={ICONS.Calender} width={20} height={20} />
                    <Text style={[styles.timeText, { color: colors.text }]}>
                      {appointmentEndTime}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Status Selection */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  Status <Text style={styles.required}>*</Text>
                </Text>
                <View style={styles.statusContainer}>
                  <TouchableOpacity
                    style={[
                      styles.statusButton,
                      {
                        backgroundColor:
                          status === "CONFIRMED"
                            ? colors.primaryBase
                            : isDarkMode
                            ? colors.cardBackground
                            : colors.white,
                        borderColor: colors.border,
                      },
                    ]}
                    onPress={() => setStatus("CONFIRMED")}
                  >
                    <Text
                      style={[
                        styles.statusText,
                        status === "CONFIRMED" && { color: colors.white },
                        status !== "CONFIRMED" && { color: colors.text },
                      ]}
                    >
                      Confirmed
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.statusButton,
                      {
                        backgroundColor:
                          status === "CANCELED"
                            ? colors.stateerrorbase
                            : isDarkMode
                            ? colors.cardBackground
                            : colors.white,
                        borderColor: colors.border,
                      },
                    ]}
                    onPress={() => setStatus("CANCELED")}
                  >
                    <Text
                      style={[
                        styles.statusText,
                        status === "CANCELED" && { color: colors.white },
                        status !== "CANCELED" && { color: colors.text },
                      ]}
                    >
                      Canceled
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.statusButton,
                      {
                        backgroundColor:
                          status === "PENDING"
                            ? colors.primaryBase
                            : isDarkMode
                            ? colors.cardBackground
                            : colors.white,
                        borderColor: colors.border,
                      },
                    ]}
                    onPress={() => setStatus("PENDING")}
                  >
                    <Text
                      style={[
                        styles.statusText,
                        status === "PENDING" && { color: colors.white },
                        status !== "PENDING" && { color: colors.text },
                      ]}
                    >
                      Pending
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Action Buttons */}
              <View style={styles.buttonContainer}>
                {/* <TouchableOpacity
                  style={[
                    styles.deleteButton,
                    {
                      backgroundColor: colors.stateerrorbase,
                    },
                  ]}
                  onPress={handleCancelAppointment}
                >
                  <Text style={styles.buttonText}>Cancel</Text>
                </TouchableOpacity> */}

                <TouchableOpacity
                  style={[
                    styles.updateButton,
                    {
                      backgroundColor: colors.primaryBase,
                    },
                    (!title || !serviceId || !teamMemberId) && { opacity: 0.5 },
                  ]}
                  onPress={handleUpdateAppointment}
                  disabled={!title || !serviceId || !teamMemberId}
                >
                  <Text style={styles.buttonText}>Update</Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>

          {/* Time Pickers */}
          <DatePicker
            modal
            open={isStartTimePickerOpen}
            date={timeStringToDate(appointmentStartTime)}
            mode="time"
            theme={isDarkMode ? "dark" : "light"}
            onConfirm={(date) => {
              setIsStartTimePickerOpen(false);
              const formattedTime = moment(date).format("HH:mm");
              setAppointmentStartTime(formattedTime);

              // Update end time to be 30 minutes after start time
              setAppointmentEndTime(calculateDefaultEndTime(formattedTime));
            }}
            onCancel={() => setIsStartTimePickerOpen(false)}
          />

          <DatePicker
            modal
            open={isEndTimePickerOpen}
            date={timeStringToDate(appointmentEndTime)}
            mode="time"
            theme={isDarkMode ? "dark" : "light"}
            onConfirm={(date) => {
              setIsEndTimePickerOpen(false);
              setAppointmentEndTime(moment(date).format("HH:mm"));
            }}
            onCancel={() => setIsEndTimePickerOpen(false)}
          />

          <DatePicker
            modal
            open={isDatePickerOpen}
            date={new Date(appointmentDate)}
            mode="date"
            theme={isDarkMode ? "dark" : "light"}
            onConfirm={(date) => {
              setIsDatePickerOpen(false);
              setAppointmentDate(moment(date).format("YYYY-MM-DD"));
            }}
            onCancel={() => setIsDatePickerOpen(false)}
          />
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: "flex-end",
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: "85%",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: moderateScale(15),
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  closeButton: {
    padding: moderateScale(5),
  },
  formContainer: {
    padding: moderateScale(15),
  },
  inputGroup: {
    marginBottom: moderateScale(15),
  },
  label: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: moderateScale(5),
  },
  required: {
    color: "red",
  },
  inputContainer: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: moderateScale(10),
  },
  input: {
    height: 40,
  },
  timeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: moderateScale(15),
    gap: moderateScale(10),
  },
  timeInput: {
    flex: 1,
  },
  timeSelector: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: moderateScale(10),
    height: 40,
    gap: moderateScale(5),
  },
  timeText: {
    flex: 1,
    marginLeft: moderateScale(5),
  },
  statusContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: moderateScale(10),
  },
  statusButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: moderateScale(8),
    paddingHorizontal: moderateScale(5),
    alignItems: "center",
  },
  statusText: {
    fontSize: 12,
    fontWeight: "500",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: moderateScale(20),
    gap: moderateScale(10),
  },
  deleteButton: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: moderateScale(12),
    alignItems: "center",
  },
  updateButton: {
    flex: 2,
    borderRadius: 8,
    paddingVertical: moderateScale(12),
    alignItems: "center",
  },
  buttonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 16,
  },
  backToSelectionButton: {
    alignItems: "center",
    marginTop: 10,
    padding: 5,
  },
});

export default EditAppointmentModal;

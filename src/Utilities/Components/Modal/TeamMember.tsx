import React, { useState } from "react";
import {
  FlatList,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import ICONS from "../../../Assets/Icon";
import CustomIcon from "../../../Components/CustomIcon";
import { useAppSelector } from "../../../Redux/store";
import { useThemedCommonStyles } from "../../Styles/themedCommonStyles";
import { useTheme } from "../../ThemeContext";
import { useThemedTeamMemberStyles } from "./themedTeamMemberStyles";
import useTranslation from "../../../Localization/useTranslation";

const teamMembersData = [
  { id: "1", name: "<PERSON>", email: "<EMAIL>", checked: false },
  { id: "2", name: "<PERSON>", email: "<EMAIL>", checked: false },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    checked: false,
  },
  { id: "4", name: "<PERSON>", email: "<EMAIL>", checked: false },
  {
    id: "5",
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    checked: false,
  },
  { id: "6", name: "<PERSON> <PERSON>", email: "<EMAIL>", checked: false },
  { id: "7", name: "David <PERSON>", email: "<EMAIL>", checked: false },
  { id: "8", name: "<PERSON> <PERSON>", email: "<EMAIL>", checked: false },
];

const TeamMember = ({ visible, onClose, onPressMember }: any) => {
  const [search, setSearch] = useState("");
  const [teamMembers, setTeamMembers] = useState(teamMembersData);
  const [selectAll, setSelectAll] = useState(false);

  const { members } = useAppSelector((state) => state.members);
  const { colors } = useTheme();
  const styles = useThemedTeamMemberStyles();
  const themedCommonStyles = useThemedCommonStyles();

  const { appointments, clients } = useTranslation();
  const toggleCheckbox = (id: string) => {
    const updatedClients = teamMembers.map((client) =>
      client.id === id ? { ...client, checked: !client.checked } : client
    );
    setTeamMembers(updatedClients);

    const allChecked = updatedClients.every((client) => client.checked);
    setSelectAll(allChecked);
  };

  const toggleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    setTeamMembers((prevMembers) =>
      prevMembers.map((member) => ({ ...member, checked: newSelectAll }))
    );
  };

  const filteredTeamMembers = members.filter(
    (member) =>
      member.name.toLowerCase().includes(search.toLowerCase()) ||
      member.email.toLowerCase().includes(search.toLowerCase())
  );

  const renderItem = ({ item }: any) => (
    <TouchableOpacity
      style={styles.memberItem}
      onPress={() => onPressMember(item)}
      activeOpacity={0.8}
    >
      <Image source={{ uri: item.image }} style={styles.profileImage} />
      <View style={styles.memberInfo}>
        <Text style={styles.memberName}>{item.name}</Text>
        <Text style={styles.memberEmail}>{item.email}</Text>
      </View>
      <CustomIcon Icon={ICONS.ArrowRightIcon} height={13} width={13} />
    </TouchableOpacity>
  );

  return (
    <Modal visible={visible} animationType="slide" transparent>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.modalContainer}
      >
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={() => {
            Keyboard.dismiss();
            onClose();
          }}
        />
        <View style={styles.modalContent}>
          <ScrollView
            bounces={false}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* Header */}
            <View style={styles.header}>
              <View />
              <Text style={styles.headerTitle}>
                {" "}
                {appointments("teamMember")}{" "}
              </Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <CustomIcon Icon={ICONS.CrossIcon} height={15} width={15} />
              </TouchableOpacity>
            </View>

            {/* Search Bar */}
            <View style={styles.inputContainer}>
              <CustomIcon Icon={ICONS.SearchIcon} width={15} height={14.17} />
              <TextInput
                style={styles.input}
                placeholder={clients("searchClients")}
                placeholderTextColor={colors.greyText}
                value={search}
                onChangeText={(text) =>
                  setSearch(text.trim().length === 0 ? text.trim() : text)
                }
                keyboardType="email-address"
              />
            </View>

            {/* Select All Option */}
            {/* {filteredTeamMembers.length > 0 && (
              <TouchableOpacity
                style={styles.selectAllContainer}
                onPress={toggleSelectAll}
                activeOpacity={0.8}
              >
                <View
                  style={[
                    styles.checkbox,
                    selectAll && { backgroundColor: colors.primaryBase },
                  ]}
                >
                  {selectAll && (
                    <View
                      style={{
                        alignItems: "center",
                        justifyContent: "center",
                        paddingVertical: 3,
                      }}
                    >
                      <CustomIcon Icon={ICONS.Subtract} height={7} width={7} />
                    </View>
                  )}
                </View>
                <Text style={{ color: colors.greyText, fontSize: 12, fontWeight: '400' }}>
                  All Team Members
                </Text>
              </TouchableOpacity>
            )} */}

            {/* Team Members List */}
            <View style={styles.flatListContainer}>
              <FlatList
                data={filteredTeamMembers}
                keyExtractor={(item) => item.id}
                renderItem={renderItem}
                showsVerticalScrollIndicator={false}
                scrollEnabled={false}
                nestedScrollEnabled={false}
                ListEmptyComponent={() => (
                  <Text style={styles.emptyText}>No Data found</Text>
                )}
              />
            </View>
          </ScrollView>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default TeamMember;

import React, { useState, useEffect, useMemo } from "react";
import { View, Text, TouchableOpacity, Platform, FlatList } from "react-native";
import RBSheet from "react-native-raw-bottom-sheet";
import CustomIcon from "../../../Components/CustomIcon";
import ICONS from "../../../Assets/Icon";
import { moderateScale } from "../../Styles/responsiveSize";
import { useAppSelector } from "../../../Redux/store";
import { selectCategoriesWithServices } from "../../../Redux/slices/servicesSlice";
import { getCurrencySymbol } from "../../currencyUtils";
import { useTheme } from "../../ThemeContext";
import { useThemedCommonStyles } from "../../Styles/themedCommonStyles";
import { useThemedAddServiceRbSheetStyles } from "./themedAddServiceRbSheetStyles";

const AddServiceRbSheet = ({
  refRBSheet,
  selectedServices,
  setSelectedServices,
  selectedCategoryId,
}: any) => {
  // Get services by category from Redux store using new API structure
  const allCategoriesWithServices = useAppSelector(
    selectCategoriesWithServices
  );
  const { colors } = useTheme();
  const styles = useThemedAddServiceRbSheetStyles();
  const themedCommonStyles = useThemedCommonStyles();

  // Filter categories based on selectedCategoryId if provided (for package creation)
  // Use useMemo to prevent infinite loops caused by array recreation
  const categoriesWithServices = useMemo(() => {
    return selectedCategoryId
      ? allCategoriesWithServices.filter(
          (category: any) => category._id === selectedCategoryId
        )
      : allCategoriesWithServices;
  }, [allCategoriesWithServices, selectedCategoryId]);

  // Initialize selected category state
  const [selectedCategory, setSelectedCategory] = useState("");

  // Set initial category when categories are loaded
  useEffect(() => {
    if (
      categoriesWithServices &&
      categoriesWithServices.length > 0 &&
      !selectedCategory
    ) {
      setSelectedCategory(categoriesWithServices[0]._id);
    }
  }, [categoriesWithServices, selectedCategory]);

  // Track selected services with local state
  const [selectedServiceIds, setSelectedServiceIds] = useState<string[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isUpdatingFromParent, setIsUpdatingFromParent] = useState(false);

  // Initialize and update selected services when selectedServices prop changes
  useEffect(() => {
    if (!isUpdatingFromParent) {
      if (selectedServices && selectedServices.length > 0) {
        const serviceIds = selectedServices.map((service: any) => {
          // Handle both API service structure and formatted service structure
          return service.serviceId || service._id || service.id;
        });

        // Only update if the service IDs have actually changed
        setSelectedServiceIds((prevIds) => {
          const newIdsSet = new Set(serviceIds);
          const prevIdsSet = new Set(prevIds);

          // Check if arrays are different
          if (
            newIdsSet.size !== prevIdsSet.size ||
            !serviceIds.every((id: string) => prevIdsSet.has(id))
          ) {
            return serviceIds;
          }
          return prevIds; // No change needed
        });
      } else {
        setSelectedServiceIds((prevIds) => (prevIds.length > 0 ? [] : prevIds));
      }

      if (!isInitialized) {
        setIsInitialized(true);
      }
    }
  }, [selectedServices, isInitialized, isUpdatingFromParent]);

  // Toggle service selection
  const toggleServiceSelection = (serviceId: string) => {
    setSelectedServiceIds((prevSelectedIds) => {
      if (prevSelectedIds.includes(serviceId)) {
        return prevSelectedIds.filter((id) => id !== serviceId);
      } else {
        return [...prevSelectedIds, serviceId];
      }
    });
  };

  // Get services for the selected category
  const categoryData = categoriesWithServices.find(
    (category: any) => category._id === selectedCategory
  );

  // Get services list with selection state
  const serviceList = categoryData
    ? categoryData.services.map((service: any) => ({
        ...service,
        selected:
          selectedServiceIds.includes(service._id) ||
          selectedServiceIds.includes(service.serviceId),
      }))
    : [];

  // Memoize the available services to prevent unnecessary recalculations
  const allAvailableServices = useMemo(() => {
    return categoriesWithServices.flatMap((category: any) => category.services);
  }, [categoriesWithServices]);

  // Update parent component's selected services when selection changes (but not during initialization)
  useEffect(() => {
    if (setSelectedServices && isInitialized) {
      setIsUpdatingFromParent(true);

      // Get newly selected services from API
      const newlySelectedServices = allAvailableServices.filter(
        (service: any) =>
          selectedServiceIds.includes(service._id) ||
          selectedServiceIds.includes(service.serviceId)
      );

      // Format newly selected services to match the expected structure
      const formattedNewServices = newlySelectedServices.map(
        (service: any) => ({
          id: service._id,
          _id: service._id,
          businessName: service.name,
          name: service.name,
          category: service.categoryName || "Unknown",
          description: service.description || "",
          duration: `${service.duration} min`,
          price: service.price,
          currency: service.currency,
          priceType: service.priceType || "Fixed price",
        })
      );

      // Simply use the formatted new services - no merging needed
      // The parent component already has the current state of services
      setSelectedServices(formattedNewServices);

      // Reset the flag after a short delay
      setTimeout(() => setIsUpdatingFromParent(false), 100);
    }
  }, [selectedServiceIds, allAvailableServices, isInitialized]);

  return (
    <View style={styles.container}>
      <RBSheet
        ref={refRBSheet}
        height={Platform.OS === "ios" ? 700 : 550}
        openDuration={300}
        closeDuration={200}
        customStyles={{ container: styles.sheetContainer }}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Add services to package</Text>
          <TouchableOpacity onPress={() => refRBSheet.current?.close()}>
            <CustomIcon Icon={ICONS.CrossIcon} width={12} height={12} />
          </TouchableOpacity>
        </View>

        {/* Category Tabs - Only show if there are multiple categories or no specific category filter */}
        {(!selectedCategoryId || categoriesWithServices.length > 1) && (
          <View style={styles.tabsContainer}>
            {categoriesWithServices && categoriesWithServices.length > 0 ? (
              <FlatList
                showsHorizontalScrollIndicator={false}
                horizontal
                data={categoriesWithServices}
                keyExtractor={(item: any) => item._id}
                renderItem={({ item }: { item: any }) => (
                  <TouchableOpacity
                    style={
                      selectedCategory === item._id
                        ? styles.activeTab
                        : styles.inactiveTab
                    }
                    onPress={() => setSelectedCategory(item._id)}
                  >
                    <Text
                      style={{
                        fontSize: 14,
                        fontWeight: "400",
                        color:
                          selectedCategory === item._id
                            ? colors.primaryBase
                            : colors.text,
                      }}
                    >
                      {item.name}
                    </Text>
                  </TouchableOpacity>
                )}
              />
            ) : (
              <Text style={styles.emptyText}>No categories available</Text>
            )}
          </View>
        )}

        {/* Services List */}
        <View>
          <Text style={styles.categoryHeader}>
            {categoryData?.name || "No category selected"}
          </Text>
          {serviceList.length === 0 ? (
            <Text style={styles.emptyText}>
              {selectedCategory
                ? "No services in this category"
                : "Please select a category to view services"}
            </Text>
          ) : (
            <FlatList
              data={serviceList}
              showsVerticalScrollIndicator={false}
              keyExtractor={(item: any) => item._id.toString()}
              renderItem={({ item }: { item: any }) => (
                <View style={styles.serviceItem}>
                  <View>
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        gap: moderateScale(5),
                      }}
                    >
                      <Text style={themedCommonStyles.font14}>{item.name}</Text>
                    </View>
                    <Text style={themedCommonStyles.font14greytext}>
                      {getCurrencySymbol(item.currency)}
                      {item.price} · {item.duration} min
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={[
                      styles.selectButton,
                      item.selected && styles.selectedButton,
                    ]}
                    onPress={() => toggleServiceSelection(item._id)}
                  >
                    {item.selected ? (
                      <CustomIcon
                        Icon={ICONS.CheckRightIcon}
                        width={15}
                        height={15}
                      />
                    ) : (
                      <Text style={themedCommonStyles.font20main}>+</Text>
                    )}
                  </TouchableOpacity>
                </View>
              )}
            />
          )}
        </View>
      </RBSheet>
    </View>
  );
};

export default AddServiceRbSheet;

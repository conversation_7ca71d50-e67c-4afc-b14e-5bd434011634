import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";

export const CategoryData = [
  {
    id: "1",
    name: "<PERSON>",
    description: "Haircuts and grooming for everyone.",
  },
  {
    id: "2",
    name: "<PERSON>",
    description: "Beard grooming and shaving services.",
  },
  {
    id: "3",
    name: "<PERSON><PERSON>",
    description: "Combination of hair and beard services.",
  },
  {
    id: "4",
    name: "Skin Care",
    description: "Facials and skin treatments.",
  },
  {
    id: "5",
    name: "Nails",
    description: "Manicures and pedicures.",
  },
];

type CategoryInterface = {
  id: string;
  name: string;
  description: string;
};

// Define a type for the slice state
interface CategorySliceState {
  categories: CategoryInterface[];
}

// Define the initial state using that type
const initialState: CategorySliceState = {
  categories: [], // Initialize with your static data
};

export const categorySlice = createSlice({
  name: "categories",
  initialState,
  reducers: {
    addCategory: (state, action: PayloadAction<CategoryInterface>) => {
      // Check if category with this ID already exists (for updating)
      const existingIndex = state.categories.findIndex(
        (category) => category.id === action.payload.id
      );

      if (existingIndex >= 0) {
        // Update existing category
        state.categories[existingIndex] = action.payload;
      } else {
        // Add new category
        state.categories.push(action.payload);
      }
    },
    updateCategory: (state, action: PayloadAction<CategoryInterface>) => {
      const existingIndex = state.categories.findIndex(
        (category) => category.id === action.payload.id
      );

      if (existingIndex >= 0) {
        // Update existing category
        state.categories[existingIndex] = action.payload;
      }
    },
    setCategories: (state, action: PayloadAction<CategoryInterface[]>) => {
      state.categories = action.payload;
    },
    deleteCategories: (state, action: PayloadAction<string[]>) => {
      state.categories = state.categories.filter(
        (category) => !action.payload.includes(category.id)
      );
    },
    resetCategories: (state) => {
      state.categories = [];
    },
  },
});

export const {
  addCategory,
  deleteCategories,
  setCategories,
  updateCategory,
  resetCategories,
} = categorySlice.actions;

export default categorySlice.reducer;

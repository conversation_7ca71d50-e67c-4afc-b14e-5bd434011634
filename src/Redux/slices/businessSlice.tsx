import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Asset } from "react-native-image-picker";

// Define types for business data
export interface TimeSlot {
  startTime: string;
  endTime: string;
}

export interface DaySchedule {
  day: string;
  isChecked: boolean;
  isClosed: boolean;
  timeSlots: TimeSlot[];
}

export interface BusinessDetails {
  businessName: string;
  businessEmail: string;
  description: string;
  phoneNumber: string;
  countryCode: string;
  callingCode: string;
  websiteUrl: string;
  businessLogo: Asset | null;
}

export interface LocationInfo {
  country: string;
  city: string;
  region: string;
  streetAddress: string;
}

export interface ContactInfo {
  email: string;
  facebook: string;
  instagram: string;
}

export interface BusinessState {
  details: BusinessDetails;
  location: LocationInfo;
  openingTimes: DaySchedule[];
  contacts: ContactInfo;
  selectedServices: string[];
  isSetupComplete: boolean;
}

// Define initial state
const initialState: BusinessState = {
  details: {
    businessName: "",
    businessEmail: "",
    description: "",
    phoneNumber: "",
    countryCode: "IN",
    callingCode: "91",
    websiteUrl: "",
    businessLogo: null,
  },
  location: {
    country: "",
    city: "",
    region: "",
    streetAddress: "",
  },
  openingTimes: [
    {
      day: "Monday",
      isChecked: true,
      isClosed: false,
      timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
    },
    {
      day: "Tuesday",
      isChecked: true,
      isClosed: false,
      timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
    },
    {
      day: "Wednesday",
      isChecked: true,
      isClosed: false,
      timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
    },
    {
      day: "Thursday",
      isChecked: true,
      isClosed: false,
      timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
    },
    {
      day: "Friday",
      isChecked: true,
      isClosed: false,
      timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
    },
    {
      day: "Saturday",
      isChecked: false,
      isClosed: true,
      timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
    },
    {
      day: "Sunday",
      isChecked: false,
      isClosed: true,
      timeSlots: [{ startTime: "09:00", endTime: "17:00" }],
    },
  ],
  contacts: {
    email: "",
    facebook: "",
    instagram: "",
  },
  selectedServices: [],
  isSetupComplete: false,
};

// Create the slice
export const businessSlice = createSlice({
  name: "business",
  initialState,
  reducers: {
    // Update business details
    updateBusinessDetails: (state, action: PayloadAction<Partial<BusinessDetails>>) => {
      state.details = { ...state.details, ...action.payload };
    },
    
    // Update location information
    updateLocationInfo: (state, action: PayloadAction<Partial<LocationInfo>>) => {
      state.location = { ...state.location, ...action.payload };
    },
    
    // Update opening times
    updateOpeningTimes: (state, action: PayloadAction<DaySchedule[]>) => {
      state.openingTimes = action.payload;
    },
    
    // Update contact information
    updateContactInfo: (state, action: PayloadAction<Partial<ContactInfo>>) => {
      state.contacts = { ...state.contacts, ...action.payload };
    },
    
    // Update selected services
    updateSelectedServices: (state, action: PayloadAction<string[]>) => {
      state.selectedServices = action.payload;
    },
    
    // Set setup completion status
    setSetupComplete: (state, action: PayloadAction<boolean>) => {
      state.isSetupComplete = action.payload;
    },
    
    // Reset business data
    resetBusinessData: (state) => {
      return initialState;
    },
  },
});

// Export actions
export const {
  updateBusinessDetails,
  updateLocationInfo,
  updateOpeningTimes,
  updateContactInfo,
  updateSelectedServices,
  setSetupComplete,
  resetBusinessData,
} = businessSlice.actions;

// Export reducer
export default businessSlice.reducer;

import { createSlice, PayloadAction, createSelector } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { Service } from "./servicesSlice";
import {
  GetAllPackagesApiResponse,
  Package as ApiPackage,
  Service as ApiPackageService,
} from "../../Services/ApiResponse";

// Define the type for a package (keeping the old structure for backward compatibility)
export interface Package {
  id: string;
  packageName: string;
  category: string;
  description: string;
  duration: string;
  customMinutes: string;
  isCustomDuration: boolean;
  priceType: string;
  currency: string;
  price: number;
  services: Service[];
}

// Define the new API-based package structure
export interface ApiPackageStructure {
  _id: string;
  name: string;
  categoryId: string;
  categoryName: string;
  description: string;
  services: ApiPackageService[];
  duration: number;
  priceType: string;
  price: number;
  maxPrice: any;
  discountPercentage: number;
  discountAmount: number;
  finalPrice: number;
  currency: string;
  businessId: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

// Define the initial state
interface PackagesState {
  packages: ApiPackageStructure[];
  isLoading: boolean;
}

const initialState: PackagesState = {
  packages: [],
  isLoading: false,
};

// Create the slice
const packagesSlice = createSlice({
  name: "packages",
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setPackages: (state, action: PayloadAction<ApiPackageStructure[]>) => {
      state.packages = action.payload;
    },
    reorderPackages: (state, action: PayloadAction<ApiPackageStructure[]>) => {
      state.packages = action.payload;
    },
    // Legacy actions for backward compatibility (can be removed later)
    addPackage: (_state, _action: PayloadAction<Omit<Package, "id">>) => {
      console.warn("addPackage action is deprecated. Use API calls instead.");
    },
    updatePackage: (_state, _action: PayloadAction<Package>) => {
      console.warn(
        "updatePackage action is deprecated. Use API calls instead."
      );
    },
    deletePackage: (_state, _action: PayloadAction<string>) => {
      _state.packages = _state.packages.filter(
        (packageItem) => packageItem._id !== _action.payload
      );
    },
    resetPackages: (state) => {
      state.packages = [];
    },
  },
});

// Export actions
export const {
  setLoading,
  setPackages,
  reorderPackages,
  addPackage,
  updatePackage,
  deletePackage,
  resetPackages,
} = packagesSlice.actions;

// Export selectors
export const selectPackages = (state: RootState) => state.packages.packages;

export const selectPackagesLoading = (state: RootState) =>
  state.packages.isLoading;

// Memoized selector for packages by category (updated for new API structure)
export const selectPackagesByCategory = createSelector(
  [selectPackages],
  (packages) => {
    const categories = Array.from(
      new Set(packages.map((pkg) => pkg.categoryName))
    );
    return categories
      .map((category) => ({
        category,
        packages: packages.filter((pkg) => pkg.categoryName === category),
      }))
      .filter((category) => category.packages.length > 0);
  }
);

// Memoized selector for packages without categorization (for backward compatibility)
export const selectPackagesFlat = createSelector(
  [selectPackages],
  (packages) => packages
);

// Export reducer
export default packagesSlice.reducer;

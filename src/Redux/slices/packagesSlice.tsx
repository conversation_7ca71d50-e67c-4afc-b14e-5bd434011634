import { createSlice, PayloadAction, createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store';
import { Service } from './servicesSlice';
import { samplePackages } from '../sampleData';

// Define the type for a package
export interface Package {
  id: string;
  packageName: string;
  category: string;
  description: string;
  duration: string;
  customMinutes: string;
  isCustomDuration: boolean;
  priceType: string;
  currency: string;
  price: number;
  services: Service[];
}

// Define the initial state
interface PackagesState {
  packages: Package[];
}

const initialState: PackagesState = {
  packages: samplePackages,
};

// Create the slice
const packagesSlice = createSlice({
  name: "packages",
  initialState,
  reducers: {
    addPackage: (state, action: PayloadAction<Omit<Package, "id">>) => {
      const newPackage: Package = {
        ...action.payload,
        id: `${Date.now()}`,
      };
      state.packages.push(newPackage);
    },
    updatePackage: (state, action: PayloadAction<Package>) => {
      const index = state.packages.findIndex(
        (pkg) => pkg.id === action.payload.id
      );
      if (index !== -1) {
        state.packages[index] = action.payload;
      }
    },
    deletePackage: (state, action: PayloadAction<string>) => {
      state.packages = state.packages.filter(
        (pkg) => pkg.id !== action.payload
      );
    },
    reorderPackages: (state, action: PayloadAction<Package[]>) => {
      state.packages = action.payload;
    },

    setPackages: (state, action: PayloadAction<Package[]>) => {
      state.packages = action.payload;
    },
  },
});

// Export actions
export const {
  addPackage,
  updatePackage,
  deletePackage,
  reorderPackages,
  setPackages,
} = packagesSlice.actions;

// Export selectors
export const selectPackages = (state: RootState) => state.packages.packages;

// Memoized selector for packages by category (used for backward compatibility)
export const selectPackagesByCategory = createSelector(
  [selectPackages],
  (packages) => {
    const categories = Array.from(new Set(packages.map((pkg) => pkg.category)));
    return categories
      .map((category) => ({
        category,
        packages: packages.filter((pkg) => pkg.category === category),
      }))
      .filter((category) => category.packages.length > 0);
  }
);

// Memoized selector for packages without categorization
export const selectPackagesFlat = createSelector(
  [selectPackages],
  (packages) => packages
);

// Export reducer
export default packagesSlice.reducer;

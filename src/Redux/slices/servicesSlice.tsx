import { createSlice, PayloadAction, createSelector } from "@reduxjs/toolkit";
import { RootState } from "../store";
import {
  CategoriesWithService,
  Service as ApiService,
} from "../../Services/ApiResponse";

// Define the type for a service (keeping the old structure for backward compatibility)
export interface Service {
  id: string;
  businessName: string;
  category: string;
  description: string;
  duration: string;
  customMinutes: string;
  isCustomDuration: boolean;
  priceType: string;
  currency: string;
  price: number | string;
  teamMembers: any;
}

// Define the new API-based service category structure
export interface ApiServiceCategory {
  _id: string;
  name: string;
  description: string;
  isGlobal: boolean;
  services: ApiService[];
  icon?: string;
}

// Define the structure for a category item that DraggableFlatList expects
export interface ServiceCategoryItem {
  _id: string;
  name: string;
  description: string;
  isGlobal: boolean;
  services: ApiService[];
  icon?: string;
}

// Define the initial state
interface ServicesState {
  categoriesWithServices: ApiServiceCategory[];
  isLoading: boolean;
}

const initialState: ServicesState = {
  categoriesWithServices: [],
  isLoading: false,
};

// Create the slice
const servicesSlice = createSlice({
  name: "services",
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setCategoriesWithServices: (
      state,
      action: PayloadAction<CategoriesWithService[]>
    ) => {
      state.categoriesWithServices = action.payload;
    },
    reorderCategories: (
      state,
      action: PayloadAction<ServiceCategoryItem[]>
    ) => {
      state.categoriesWithServices = action.payload;
    },
    // Legacy actions for backward compatibility (can be removed later)
    addService: (_state, _action: PayloadAction<Omit<Service, "id">>) => {
      // For now, we'll keep this for backward compatibility
      // In the future, this should be handled by API calls
      console.warn("addService action is deprecated. Use API calls instead.");
    },
    updateService: (_state, _action: PayloadAction<Service>) => {
      console.warn(
        "updateService action is deprecated. Use API calls instead."
      );
    },
    deleteService: (_state, _action: PayloadAction<string>) => {
      console.warn(
        "deleteService action is deprecated. Use API calls instead."
      );
    },
    setServices: (state, action: PayloadAction<CategoriesWithService[]>) => {
      state.categoriesWithServices = action.payload;
    },
  },
});

// Export actions
export const {
  setLoading,
  setCategoriesWithServices,
  reorderCategories,
  setServices,
  addService,
  updateService,
  deleteService,
} = servicesSlice.actions;

// Export selectors
export const selectCategoriesWithServices = (state: RootState) =>
  state.services.categoriesWithServices;

export const selectServicesLoading = (state: RootState) =>
  state.services.isLoading;

// Memoized selector for services by category (for backward compatibility)
export const selectServicesByCategory = createSelector(
  [selectCategoriesWithServices],
  (categoriesWithServices) => {
    return categoriesWithServices.map((category) => ({
      _id: category._id,
      name: category.name,
      description: category.description,
      isGlobal: category.isGlobal,
      services: category.services,
      icon: category.icon,
    }));
  }
);

// Export reducer
export default servicesSlice.reducer;

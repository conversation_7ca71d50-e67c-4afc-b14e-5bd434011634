import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";

export const ClientData = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    number: "+1 555-123-4567",
    image:
      "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    checked: false,
  },
  {
    id: "2",
    name: "<PERSON><PERSON>",
    number: "+49 176-5552-8899",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    checked: false,
  },
  {
    id: "3",
    name: "<PERSON>lsen",
    number: "+47 912-34-567",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1568602471122-7832951cc4c5?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    checked: false,
  },
  {
    id: "4",
    name: "Miloš Petrović",
    number: "+381 63-555-777",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    checked: false,
  },
  {
    id: "5",
    name: "Viktor Horváth",
    number: "+36 30-555-6789",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1463453091185-61582044d556?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    checked: false,
  },
  {
    id: "6",
    name: "Enis Ho",
    number: "+852 5555-7890",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    checked: false,
  },
  {
    id: "7",
    name: "Theo Van Dijk",
    number: "+31 6-5555-4321",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1504257432389-52343af06ae3?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    checked: false,
  },
  {
    id: "8",
    name: "Matteo Rossi",
    number: "+39 333-555-7890",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1499996860823-5214fcc65f8f?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    checked: false,
  },
  {
    id: "9",
    name: "David Smith",
    number: "+1 555-987-6543",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1492562080023-ab3db95bfbce?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    checked: false,
  },
  {
    id: "10",
    name: "Olivia Williams",
    number: "+1 555-234-5678",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    checked: false,
  },
  {
    id: "11",
    name: "James Brown",
    number: "+1 555-345-6789",
    email: "<EMAIL>",
    image:
      "https://images.unsplash.com/photo-1500048993953-d23a436266cf?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    checked: false,
  },
];

// Define a type for the slice state
interface ClientSlice {
  clients: any[];
}

// Define the initial state using that type
const initialState: ClientSlice = {
  clients: [],
};

export const clientSlice = createSlice({
  name: "clients",
  initialState,
  reducers: {
    setClients: (state, action: PayloadAction<any[]>) => {
      state.clients = action.payload;
    },
    setNewClient: (state, action: PayloadAction<any>) => {
      state.clients.push(action.payload);
    },
    updateClient: (state, action: PayloadAction<any>) => {
      const updatedClient = action.payload;
      const index = state.clients.findIndex(
        (client) => client.id === updatedClient.id
      );
      if (index !== -1) {
        state.clients[index] = updatedClient; // Replace the client with the updated one
      }
    },
    deleteClients: (state, action: PayloadAction<string[]>) => {
      state.clients = state.clients.filter(
        (client) => !action.payload.includes(client.id)
      );
    },
  },
});

export const { setClients, setNewClient, updateClient, deleteClients } =
  clientSlice.actions;

export default clientSlice.reducer;

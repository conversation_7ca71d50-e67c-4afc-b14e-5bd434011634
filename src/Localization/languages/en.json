{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "next": "Next", "back": "Back", "done": "Done", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "search": "Search", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "required": "Required", "optional": "Optional", "select": "Select", "selectAll": "Select All", "clear": "Clear", "apply": "Apply", "reset": "Reset", "submit": "Submit", "continue": "Continue", "skip": "<PERSON><PERSON>", "retry": "Retry", "refresh": "Refresh", "update": "Update", "create": "Create", "remove": "Remove", "view": "View", "share": "Share", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "signup": "Sign Up", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "newPassword": "New Password", "currentPassword": "Current Password", "rememberMe": "Remember Me", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "signupSuccess": "Account created successfully", "signupError": "Failed to create account", "invalidEmail": "Invalid email address", "invalidPassword": "Password must be at least 6 characters", "passwordMismatch": "Passwords do not match", "emailRequired": "Email is required", "passwordRequired": "Password is required", "accountExists": "Account already exists", "accountNotFound": "Account not found", "invalidCredentials": "Invalid email or password", "passwordResetSent": "Password reset link sent to your email", "passwordResetSuccess": "Password reset successful", "verificationCode": "Verification Code", "enterVerificationCode": "Enter the verification code sent to your email", "resendCode": "Resend Code", "codeExpired": "Verification code has expired", "invalidCode": "Invalid verification code"}, "navigation": {"overview": "Overview", "calendar": "Calendar", "clients": "Clients", "manage": "Manage", "profile": "Profile", "settings": "Settings", "appointments": "Appointments", "services": "Services", "team": "Team", "categories": "Categories", "packages": "Packages", "permissions": "Permissions"}, "appointments": {"appointment": "Appointment", "appointments": "Appointments", "newAppointment": "New Appointment", "editAppointment": "Edit Appointment", "deleteAppointment": "Delete Appointment", "appointmentDetails": "Appointment Details", "myAppointments": "My Appointments", "upcomingAppointments": "Upcoming Appointments", "pastAppointments": "Past Appointments", "todayAppointments": "Today's Appointments", "noAppointments": "No appointments", "noAppointmentsToday": "No appointments for today", "noUpcomingAppointments": "No upcoming appointments", "appointmentCreated": "Appointment created successfully", "appointmentUpdated": "Appointment updated successfully", "appointmentDeleted": "Appointment deleted successfully", "appointmentCancelled": "Appointment cancelled", "appointmentConfirmed": "Appointment confirmed", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration", "date": "Date", "time": "Time", "client": "Client", "service": "Service", "teamMember": "Team Member", "status": "Status", "confirmed": "Confirmed", "cancelled": "Cancelled", "pending": "Pending", "completed": "Completed", "notes": "Notes", "addNotes": "Add Notes", "selectClient": "Select Client", "selectService": "Select Service", "selectTeamMember": "Select Team Member", "selectDate": "Select Date", "selectTime": "Select Time", "selectFromExistingClients": "Select from existing clients", "backToClientSelection": "Back to client selection", "clientName": "Client Name", "clientPhone": "Client Phone", "phoneNumber": "Phone Number"}, "clients": {"client": "Client", "clients": "Clients", "newClient": "New Client", "addClient": "Add Client", "editClient": "Edit Client", "deleteClient": "Delete Client", "clientDetails": "Client Details", "manageClients": "Manage Clients", "noClients": "No clients", "clientCreated": "Client created successfully", "clientUpdated": "Client updated successfully", "clientDeleted": "Client deleted successfully", "firstName": "First Name", "lastName": "Last Name", "fullName": "Full Name", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "country": "Country", "notes": "Notes", "profilePicture": "Profile Picture", "selectPhoto": "Select Photo", "takePhoto": "Take Photo", "chooseFromGallery": "Choose from Gallery", "removePhoto": "Remove Photo"}, "services": {"service": "Service", "services": "Services", "newService": "New Service", "addService": "Add Service", "editService": "Edit Service", "deleteService": "Delete Service", "serviceDetails": "Service Details", "manageServices": "Manage Services", "servicesList": "Services List", "noServices": "No services", "serviceCreated": "Service created successfully", "serviceUpdated": "Service updated successfully", "serviceDeleted": "Service deleted successfully", "serviceName": "Service Name", "serviceDescription": "Service Description", "servicePrice": "Service Price", "serviceDuration": "Service Duration", "serviceCategory": "Service Category", "price": "Price", "duration": "Duration", "category": "Category", "description": "Description", "minutes": "minutes", "hours": "hours", "selectCategory": "Select Category"}, "team": {"team": "Team", "teamMember": "Team Member", "teamMembers": "Team Members", "newTeamMember": "New Team Member", "addTeamMember": "Add Team Member", "editTeamMember": "Edit Team Member", "deleteTeamMember": "Delete Team Member", "teamMemberDetails": "Team Member Details", "manageTeam": "Manage Team", "noTeamMembers": "No team members", "teamMemberCreated": "Team member added successfully", "teamMemberUpdated": "Team member updated successfully", "teamMemberDeleted": "Team member removed successfully", "role": "Role", "permissions": "Permissions", "selectRole": "Select Role", "admin": "Admin", "manager": "Manager", "staff": "Staff", "owner": "Owner"}, "business": {"business": "Business", "businessDetails": "Business Details", "businessName": "Business Name", "businessType": "Business Type", "businessAddress": "Business Address", "businessPhone": "Business Phone", "businessEmail": "Business Email", "businessWebsite": "Business Website", "businessDescription": "Business Description", "openingHours": "Opening Hours", "closingHours": "Closing Hours", "workingDays": "Working Days", "businessSetup": "Business Setup", "setupBusiness": "Setup Business", "createBusiness": "Create Business", "joinBusiness": "Join Business", "businessCreated": "Business created successfully", "businessUpdated": "Business updated successfully", "location": "Location", "contactDetails": "Contact Details", "operatingHours": "Operating Hours", "street": "Street", "currency": "<PERSON><PERSON><PERSON><PERSON>", "selectCurrency": "Select Currency"}, "calendar": {"calendar": "Calendar", "today": "Today", "tomorrow": "Tomorrow", "yesterday": "Yesterday", "thisWeek": "This Week", "nextWeek": "Next Week", "thisMonth": "This Month", "nextMonth": "Next Month", "viewMode": "View Mode", "hourly": "Hourly", "weekly": "Weekly", "monthly": "Monthly", "selectMonth": "Select Month", "currentTime": "Current Time", "noAppointmentsForDate": "No appointments for {{date}}", "checkingNetwork": "Checking network...", "noInternetConnection": "No internet connection", "tryAgain": "Try again", "quietDays": "Seems like you have a few quiet days... How about booking some 'me time'?"}, "settings": {"settings": "Settings", "language": "Language", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "dark": "Dark", "light": "Light", "auto": "Auto", "notifications": "Notifications", "privacy": "Privacy", "security": "Security", "about": "About", "version": "Version", "privacyPolicy": "Privacy Policy", "contactSupport": "Contact Support", "rateApp": "Rate App", "shareApp": "Share App", "deleteAccount": "Delete Account", "signOut": "Sign Out", "appearance": "Appearance", "general": "General", "account": "Account", "help": "Help", "feedback": "<PERSON><PERSON><PERSON>", "logout": "Log Out", "termsOfService": "Terms of Service", "password": "Password"}, "categories": {"category": "Category", "categories": "Categories", "newCategory": "New Category", "addCategory": "Add Category", "editCategory": "Edit Category", "deleteCategory": "Delete Category", "categoryDetails": "Category Details", "manageCategories": "Manage Categories", "noCategories": "No categories", "categoryCreated": "Category created successfully", "categoryUpdated": "Category updated successfully", "categoryDeleted": "Category deleted successfully", "categoryName": "Category Name", "categoryDescription": "Category Description", "categoryColor": "Category Color", "selectColor": "Select Color"}, "packages": {"package": "Package", "packages": "Packages", "newPackage": "New Package", "addPackage": "Add Package", "editPackage": "Edit Package", "deletePackage": "Delete Package", "packageDetails": "Package Details", "managePackages": "Manage Packages", "noPackages": "No packages", "packageCreated": "Package created successfully", "packageUpdated": "Package updated successfully", "packageDeleted": "Package deleted successfully", "packageName": "Package Name", "packageDescription": "Package Description", "packagePrice": "Package Price", "packageServices": "Package Services", "addServices": "Add Services", "removeService": "Remove Service", "totalPrice": "Total Price", "discount": "Discount", "finalPrice": "Final Price"}, "errors": {"networkError": "Network error occurred", "serverError": "Server error occurred", "unknownError": "An unknown error occurred", "validationError": "Validation error", "authenticationError": "Authentication error", "permissionError": "Permission denied", "notFoundError": "Resource not found", "timeoutError": "Request timeout", "connectionError": "Connection error", "tryAgainLater": "Please try again later", "contactSupport": "Please contact support if the problem persists"}, "validation": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "minLength": "Must be at least {{min}} characters", "maxLength": "Must be no more than {{max}} characters", "invalidFormat": "Invalid format", "mustMatch": "Fields must match", "invalidDate": "Please enter a valid date", "invalidTime": "Please enter a valid time", "invalidPrice": "Please enter a valid price", "invalidDuration": "Please enter a valid duration"}, "time": {"minute": "minute", "minutes": "minutes", "hour": "hour", "hours": "hours", "day": "day", "days": "days", "week": "week", "weeks": "weeks", "month": "month", "months": "months", "year": "year", "years": "years", "am": "AM", "pm": "PM", "now": "Now", "soon": "Soon", "later": "Later"}}
{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "next": "Next", "back": "Back", "done": "Done", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "open": "Open", "search": "Search", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "required": "Required", "optional": "Optional", "select": "Select", "selectAll": "Select All", "clear": "Clear", "apply": "Apply", "reset": "Reset", "submit": "Submit", "continue": "Continue", "skip": "<PERSON><PERSON>", "retry": "Retry", "refresh": "Refresh", "update": "Update", "create": "Create", "remove": "Remove", "view": "View", "share": "Share", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "sortAZ": "A-Z", "sortZA": "Z-A", "yesDelete": "Yes, delete", "noCancel": "No, cancel", "somethingWentWrong": "Something went wrong. Please try again later.", "failedToFetchAppointments": "Failed to fetch appointments", "pleaseTryAgainLater": "Please try again later.", "addnew": "Add New", "weekly": "Weekly", "monthly": "Monthly", "hourly": "Hourly", "password": "Password", "passwordError": "Please enter your password to confirm account deactivation"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "signup": "Sign Up", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "newPassword": "New Password", "currentPassword": "Current Password", "rememberMe": "Remember Me", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "signupSuccess": "Account created successfully", "signupError": "Failed to create account", "invalidEmail": "Invalid email address", "invalidPassword": "Password must be at least 6 characters", "passwordMismatch": "Passwords do not match", "emailRequired": "Email is required", "passwordRequired": "Password is required", "accountExists": "Account already exists", "accountNotFound": "Account not found", "invalidCredentials": "Invalid email or password", "passwordResetSent": "Password reset link sent to your email", "passwordResetSuccess": "Password reset successful", "verificationCode": "Verification Code", "enterVerificationCode": "Enter the verification code sent to your email", "resendCode": "Resend Code", "codeExpired": "Verification code has expired", "invalidCode": "Invalid verification code", "accessAccount": "Access your GlamUP account", "wellnessJourney": "Your wellness journey starts here", "emailAddress": "Email Address", "emailPlaceholder": "<EMAIL>", "passwordPlaceholder": "••••••••••", "keepMeLoggedIn": "Keep me logged in", "clientNotice": "Notice: If you are a client, please switch to the", "glamupClientApp": "GlamUP Client App", "bestExperience": "for the best experience.", "noAccount": "Don't have a professional account?", "register": "Register", "createProfessionalAccount": "Create a professional account", "joinGlamUP": "Join <PERSON> and manage your business effortlessly", "pleaseEnterValidEmail": "Please enter a valid email"}, "navigation": {"overview": "Overview", "calendar": "Calendar", "clients": "Clients", "manage": "Manage", "profile": "Profile", "settings": "Settings", "appointments": "Appointments", "services": "Services", "team": "Team", "categories": "Categories", "packages": "Packages", "permissions": "Permissions"}, "appointments": {"appointment": "Appointment", "addAppointment": "Add Appointment", "editAppointment": "Edit Appointment", "updateAppointment": "Update Appointment", "service": "Service", "teamMember": "Team Member", "date": "Date", "startTime": "Start Time", "endTime": "End Time", "status": "Status", "confirmed": "Confirmed", "pending": "Pending", "canceled": "Canceled"}, "clients": {"client": "Client", "clients": "Clients", "newClient": "New Client", "addClient": "Add Client", "editClient": "Edit Client", "deleteClient": "Delete Client", "clientDetails": "Client Details", "manageClients": "Manage Clients", "noClients": "No clients", "clientCreated": "Client created successfully", "clientUpdated": "Client updated successfully", "clientDeleted": "Client deleted successfully", "firstName": "First Name", "lastName": "Last Name", "fullName": "Full Name", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "country": "Country", "notes": "Notes", "profilePicture": "Profile Picture", "selectPhoto": "Select Photo", "takePhoto": "Take Photo", "chooseFromGallery": "Choose from Gallery", "removePhoto": "Remove Photo", "searchClients": "Search by name, email or phone number", "sortAZ": "Sort A-Z", "sortZA": "Sort Z-A", "selectAll": "Select All", "deleteSelected": "Delete Selected", "confirmDelete": "Are you sure you want to delete the selected clients?", "deleteConfirmation": "This action cannot be undone", "yesDelete": "Yes, delete", "noCancel": "No, cancel", "clientsDeleted": "{{count}} clients deleted successfully", "name": "Name", "phoneNumber": "Phone Number", "emailAddress": "Email Address", "birthday": "Birthday", "gender": "Gender", "selectGender": "Select Gender", "male": "Male", "female": "Female", "other": "Other", "addNewClient": "Add New Client", "editClientDetails": "Edit Client Details", "saveClient": "Save Client", "updateClient": "Update Client", "clientInformation": "Client Information", "personalDetails": "Personal Details", "contactInformation": "Contact Information"}, "services": {"service": "Service", "services": "Services", "newService": "New Service", "addService": "Add Service", "editService": "Edit Service", "deleteService": "Delete Service", "serviceDetails": "Service Details", "manageServices": "Manage Services", "servicesList": "Services List", "serviceDurationAndPrice": "Duration & price", "noServices": "No services", "serviceCreated": "Service created successfully", "serviceUpdated": "Service updated successfully", "serviceDeleted": "Service deleted successfully", "serviceName": "Service Name", "serviceDescription": "Service Description", "servicePrice": "Service Price", "serviceDuration": "Service Duration", "serviceCategory": "Service Category", "price": "Price", "duration": "Duration", "category": "Category", "description": "Description", "minutes": "minutes", "hours": "hours", "selectCategory": "Select Category", "listOfServices": "List of Services", "listOfPackages": "List of Packages", "addPackage": "Add Package", "searchServices": "Search services...", "searchPackages": "Search packages...", "noServicesFound": "No services found", "noPackagesFound": "No packages found", "servicesTab": "Services", "packagesTab": "Packages", "serviceIcon": "Service Icon", "selectIcon": "Select Icon", "priceType": "Price Type", "fixedPrice": "Fixed Price", "priceRange": "Price Range", "minPrice": "Minimum Price", "maxPrice": "Maximum Price", "teamMembers": "Team Members", "selectTeamMembers": "Select Team Members", "currency": "<PERSON><PERSON><PERSON><PERSON>", "selectCurrency": "Select Currency", "serviceTeam": "Service Team", "addTeamMember": "Add Team Member", "removeTeamMember": "Remove Team Member", "editServiceDetails": "Edit Service Details", "saveService": "Save Service", "updateService": "Update Service", "deleteServiceConfirm": "Are you sure you want to delete this service?", "serviceDeletedSuccess": "Service deleted successfully", "customDuration": "Custom Duration", "enterMinutes": "Enter minutes", "teammebers": "Team Members", "selectTeamMembersTitle": "Select team members who can perform this service", "allTeamMembers": "ALL TEAM MEMBERS"}, "team": {"team": "Team", "teamMember": "Team Member", "teamMembers": "Team Members", "newTeamMember": "New Team Member", "addTeamMember": "Add Team Member", "editTeamMember": "Edit Team Member", "deleteTeamMember": "Delete Team Member", "teamMemberDetails": "Team Member Details", "manageTeam": "Manage Team", "noTeamMembers": "No team members", "teamMemberCreated": "Team member added successfully", "teamMemberUpdated": "Team member updated successfully", "teamMemberDeleted": "Team member removed successfully", "role": "Role", "permissions": "Permissions", "selectRole": "Select Role", "admin": "Admin", "manager": "Manager", "staff": "Staff", "owner": "Owner", "teamMembersList": "Team Members List", "manageTeamMembers": "Manage Team Members", "teamMemberPermissions": "Team Member Permissions", "searchTeamMembers": "Search team members...", "addNewTeamMember": "Add New Team Member", "editTeamMemberDetails": "Edit Team Member Details", "deleteTeamMemberConfirm": "Are you sure you want to delete this team member?", "teamMemberDeletedSuccess": "Team member deleted successfully", "memberDetails": "Member Details", "specialization": "Specialization", "joinDate": "Join Date", "employmentStatus": "Employment Status", "active": "Active", "inactive": "Inactive", "fullTime": "Full Time", "partTime": "Part Time", "contract": "Contract", "canManageAppointments": "Can Manage Appointments", "canManageClients": "Can Manage Clients", "canManageServices": "Can Manage Services", "canManageTeam": "Can Manage Team", "canManageSettings": "Can Manage Settings", "canAccessReports": "Can Access Reports", "permissionSettings": "Permission Settings", "rolePermissions": "Role Permissions", "customPermissions": "Custom Permissions", "name": "Name", "email": "Email", "phoneNumber": "Phone Number", "birthday": "Birthday", "gender": "Gender", "selectGender": "Select Gender", "male": "Male", "female": "Female", "other": "Other", "saveTeamMember": "Save", "updateTeamMember": "Update", "membersSelected": "MEMBERS SELECTED", "addTeamMembersToManage": "Add team members to manage your business together", "deleteTeamMembersTitle": "Delete Team Members", "deleteTeamMembersConfirm": "Are you sure you want to delete the selected team members? This operation cannot be undone."}, "business": {"business": "Business", "businessDetails": "Business Details", "businessName": "Business Name", "businessType": "Business Type", "businessAddress": "Business Address", "businessPhone": "Business Phone", "businessEmail": "Business Email", "businessWebsite": "Business Website", "businessDescription": "Business Description", "openingHours": "Opening Hours", "closingHours": "Closing Hours", "workingDays": "Working Days", "businessSetup": "Business Setup", "setupBusiness": "Setup Business", "createBusiness": "Create Business", "joinBusiness": "Join Business", "businessCreated": "Business created successfully", "businessUpdated": "Business updated successfully", "location": "Location", "contacts": "Contacts", "contactDetails": "Contact Details", "saveBusiness": "Save Business", "setupSteps": {"details": "Details", "services": "Services", "finalise": "Finalise", "businesses": "Businesses", "step1Title": "Business Details", "step1Subtitle": "Tell us about your business", "step2Title": "Select Services", "step2Subtitle": "Choose the services you offer", "step3Title": "Finalize Setup", "step3Subtitle": "Complete your business setup", "businessNamePlaceholder": "Enter business name", "businessEmailPlaceholder": "Enter business email", "descriptionPlaceholder": "Describe your business", "phoneNumberPlaceholder": "Enter phone number", "websiteUrlPlaceholder": "Enter website URL", "uploadLogo": "Upload Business Logo", "logoUploaded": "Logo uploaded successfully", "selectServices": "Select the services you offer", "searchServices": "Search services...", "noServicesSelected": "No services selected", "servicesSelected": "{{count}} services selected", "completeSetup": "Complete Setup", "setupComplete": "Business setup completed successfully!", "street": "Street Address", "city": "City", "region": "Region/State", "country": "Country", "postalCode": "Postal Code", "businessesList": "Businesses List", "noBusinessesAvailable": "No Businesses available currently", "businessName": "Business name", "businessEmail": "Business Email", "description": "Description", "phoneNumber": "Phone Number", "website": "Website", "websitePlaceholder": "https://example.com", "welcomeToGlamUP": "Welcome To GlamUP", "goToHome": "Go To Home", "businessDescriptionplaceholder": "Give a brief description about your business and the services you provide...", "selectCountry": "Select a country", "selectCity": "Select a city", "selectRegion": "Select a region", "selectStreet": "Enter street address", "addTimeSlot": "Add Time Slot"}, "operatingHours": "Operating Hours", "street": "Street", "currency": "<PERSON><PERSON><PERSON><PERSON>", "selectCurrency": "Select Currency"}, "calendar": {"calendar": "Calendar", "today": "Today", "tomorrow": "Tomorrow", "yesterday": "Yesterday", "thisWeek": "This Week", "nextWeek": "Next Week", "thisMonth": "This Month", "nextMonth": "Next Month", "viewMode": "View Mode", "hourly": "Hourly", "weekly": "Weekly", "monthly": "Monthly", "selectMonth": "Select Month", "currentTime": "Current Time", "noAppointmentsForDate": "No appointments for {{date}}", "checkingNetwork": "Checking network...", "noInternetConnection": "No internet connection", "tryAgain": "Try again", "quietDays": "Seems like you have a few quiet days... How about booking some 'me time'?"}, "manage": {"title": "Manage", "setupBusiness": "Setup your business", "setupBusinessSubtitle": "Setup a new account for your business", "listOfServices": "List of services", "listOfServicesSubtitle": "Manage and organize the services your business offers.", "categories": "Categories", "categoriesSubtitle": "Group your services into categories for better organization.", "team": "Team", "teamSubtitle": "Manage team members and their schedules", "manageTeamButton": "Manage team", "clients": "Clients", "clientsSubtitle": "Add and manage your client database.", "noInternetConnection": "No Internet Connection", "noInternetSubtitle": "Please check your internet connection and try again.", "tryAgain": "Try again"}, "settings": {"settings": "Settings", "language": "Language", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "dark": "Dark", "light": "Light", "auto": "Auto", "notifications": "Notifications", "privacy": "Privacy", "security": "Security", "about": "About", "version": "Version", "privacyPolicy": "Privacy Policy", "contactSupport": "Contact Support", "rateApp": "Rate App", "shareApp": "Share App", "deleteAccount": "Delete Account", "signOut": "Sign Out", "appearance": "Appearance", "general": "General", "account": "Account", "help": "Help", "feedback": "<PERSON><PERSON><PERSON>", "logout": "Log Out", "logoutConfirm": "Are you sure you want to log out?", "deleteAccountConfirm": "This action will permanently deactivate your account. Please enter your password to confirm.", "deleteAccountConfirmTitle": "Delete Account", "termsOfService": "Terms of Service", "password": "Change Password"}, "categories": {"category": "Category", "categories": "Categories", "newCategory": "New Category", "addCategory": "Add Category", "editCategory": "Edit Category", "deleteCategory": "Delete Category", "categoryDetails": "Category Details", "manageCategories": "Manage Categories", "noCategories": "No categories", "categoryCreated": "Category created successfully", "categoryUpdated": "Category updated successfully", "categoryDeleted": "Category deleted successfully", "categoryName": "Category Name", "categoryDescription": "Category Description", "categoryColor": "Category Color", "selectColor": "Select Color", "categoriesList": "Categories List", "searchCategories": "Search categories...", "addNewCategory": "Add New Category", "editCategoryDetails": "Edit Category Details", "deleteCategoryConfirm": "Are you sure you want to delete this category?", "categoryDeletedSuccess": "Category deleted successfully", "categoryIcon": "Category Icon", "saveCategoryChanges": "Save Category Changes", "updateCategory": "Update Category", "createCategory": "Create Category", "categoriesSelected": "CATEGORIES SELECTED", "noCategoriesFound": "No Categories Found", "addCategoriesToOrganize": "Add categories to organize your services better! 🎉", "addFirstCategory": "Add your first category", "deleteCategoriesTitle": "Delete categories", "deleteCategoriesConfirm": "All Services and packages associated with the selected categories will also be deleted. This operation cannot be undone.", "deleting": "Deleting..."}, "packages": {"package": "Package", "packages": "Packages", "newPackage": "New Package", "addPackage": "Add Package", "editPackage": "Edit Package", "deletePackage": "Delete Package", "packageDetails": "Package Details", "managePackages": "Manage Packages", "noPackages": "No packages", "packageCreated": "Package created successfully", "packageUpdated": "Package updated successfully", "packageDeleted": "Package deleted successfully", "packageName": "Package Name", "packageDescription": "Package Description", "packagePrice": "Package Price", "packageServices": "Package Services", "addServices": "Add Services", "removeService": "Remove Service", "totalPrice": "Total Price", "discount": "Discount", "finalPrice": "Final Price", "packageDurationAndPrice": "Duration & Price", "packageNamePlaceholder": "e.g Haircut + Beard Combo", "packageDescriptionPlaceholder": "Explain your package in more detail...", "noServicesAdded": "No services added yet. Click the button below to add services to your package.", "removeServiceTitle": "Remove Service", "removeServiceConfirm": "Are you sure you want to remove \"{serviceName}\" from this package?", "savePackage": "Save Package"}, "errors": {"networkError": "Network error occurred", "serverError": "Server error occurred", "unknownError": "An unknown error occurred", "validationError": "Validation error", "authenticationError": "Authentication error", "permissionError": "Permission denied", "notFoundError": "Resource not found", "timeoutError": "Request timeout", "connectionError": "Connection error", "tryAgainLater": "Please try again later", "contactSupport": "Please contact support if the problem persists"}, "validation": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "minLength": "Must be at least {{min}} characters", "maxLength": "Must be no more than {{max}} characters", "invalidFormat": "Invalid format", "mustMatch": "Fields must match", "invalidDate": "Please enter a valid date", "invalidTime": "Please enter a valid time", "invalidPrice": "Please enter a valid price", "invalidDuration": "Please enter a valid duration"}, "time": {"minute": "minute", "minutes": "minutes", "hour": "hour", "hours": "hours", "day": "day", "days": "days", "week": "week", "weeks": "weeks", "month": "month", "months": "months", "year": "year", "years": "years", "am": "AM", "pm": "PM", "now": "Now", "soon": "Soon", "later": "Later"}, "notifications": {"notifications": "Notifications", "noNotifications": "No notifications", "markAllAsRead": "Mark all as read", "markAsRead": "<PERSON> as read", "viewAll": "View all", "view": "View", "read": "Read", "unread": "Unread"}, "termsOfService": {"termsOfService": "Terms of Service", "lastUpdated": "Last Updated: {{date}}", "introduction": "Welcome to GlamUp! These terms and conditions outline the rules and regulations for the use of our app."}, "privacyPolicy": {"privacyPolicy": "Privacy Policy", "lastUpdated": "Last Updated: {{date}}", "introduction": "Welcome to GlamUp! This privacy policy outlines the types of information we collect and how we use it."}, "changePassword": {"changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "passwordChanged": "Password changed successfully", "passwordNotMatch": "New Password and Confirm New Password do not match", "passwordNotMatchCurrent": "New password must be different from current password", "passwordRequirements": "Password must contain at least 1 uppercase letter, 1 number, min. 8 characters", "atLeastOneUppercase": "At least 1 uppercase letter", "atLeastOneNumber": "At least 1 number", "atLeastEightCharacters": "At least 8 characters"}, "bottomTabs": {"overview": "Overview", "manage": "Manage"}}
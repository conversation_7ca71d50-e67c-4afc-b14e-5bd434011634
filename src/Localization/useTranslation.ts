import { useTranslation as useI18nTranslation } from 'react-i18next';

// Custom hook that provides type-safe translations
export const useTranslation = () => {
  const { t, i18n } = useI18nTranslation();

  // Helper function for common translations
  const translate = (key: string, options?: any): string => {
    return t(key, options) as string;
  };

  // Helper functions for specific sections
  const common = (key: string, options?: any): string => {
    return t(`common.${key}`, options) as string;
  };

  const auth = (key: string, options?: any): string => {
    return t(`auth.${key}`, options) as string;
  };

  const navigation = (key: string, options?: any): string => {
    return t(`navigation.${key}`, options) as string;
  };

  const appointments = (key: string, options?: any): string => {
    return t(`appointments.${key}`, options) as string;
  };

  const clients = (key: string, options?: any): string => {
    return t(`clients.${key}`, options) as string;
  };

  const services = (key: string, options?: any): string => {
    return t(`services.${key}`, options) as string;
  };

  const team = (key: string, options?: any): string => {
    return t(`team.${key}`, options) as string;
  };

  const business = (key: string, options?: any): string => {
    return t(`business.${key}`, options) as string;
  };

  const calendar = (key: string, options?: any): string => {
    return t(`calendar.${key}`, options) as string;
  };

  const settings = (key: string, options?: any): string => {
    return t(`settings.${key}`, options) as string;
  };

  const categories = (key: string, options?: any): string => {
    return t(`categories.${key}`, options) as string;
  };

  const packages = (key: string, options?: any): string => {
    return t(`packages.${key}`, options) as string;
  };

  const notifications = (key: string, options?: any): string => {
    return t(`notifications.${key}`, options) as string;
  };

  const errors = (key: string, options?: any): string => {
    return t(`errors.${key}`, options) as string;
  };

  const validation = (key: string, options?: any): string => {
    return t(`validation.${key}`, options) as string;
  };

  const time = (key: string, options?: any): string => {
    return t(`time.${key}`, options) as string;
  };

  const termsOfService = (key: string, options?: any): string => {
    return t(`termsOfService.${key}`, options) as string;
  };

  const privacyPolicy = (key: string, options?: any): string => {
    return t(`privacyPolicy.${key}`, options) as string;
  };

  const changePassword = (key: string, options?: any): string => {
    return t(`changePassword.${key}`, options) as string;
  };

  const manage = (key: string, options?: any): string => {
    return t(`manage.${key}`, options) as string;
  };

  const bottomTabs = (key: string, options?: any): string => {
    return t(`bottomTabs.${key}`, options) as string;
  };

  return {
    t: translate,
    i18n,
    common,
    auth,
    navigation,
    appointments,
    clients,
    services,
    team,
    business,
    calendar,
    settings,
    categories,
    packages,
    notifications,
    errors,
    validation,
    time,
    termsOfService,
    privacyPolicy,
    changePassword,
    manage,
    bottomTabs,
  };
};

export default useTranslation;
